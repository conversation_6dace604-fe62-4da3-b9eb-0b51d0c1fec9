/* Loading indicator styles */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(var(--bg-primary-rgb), 0.8);
  z-index: 100;
  backdrop-filter: blur(2px);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loading-container.active {
  opacity: 1;
  visibility: visible;
}

.loading-text {
  font-size: 32px;
  font-weight: bold;
  color: var(--accent-primary);
  display: flex;
  gap: 8px;
  letter-spacing: 2px;
}

.loading-letter {
  opacity: 0;
  transform: translateY(10px);
}

/* Animation for G */
.loading-letter:nth-child(1) {
  animation: fadeInUp 1.2s ease forwards;
  animation-delay: 0.1s;
}

/* Animation for . */
.loading-letter:nth-child(2) {
  animation: fadeInUp 1.2s ease forwards;
  animation-delay: 0.3s;
}

/* Animation for S */
.loading-letter:nth-child(3) {
  animation: fadeInUp 1.2s ease forwards;
  animation-delay: 0.5s;
}

/* Animation for . */
.loading-letter:nth-child(4) {
  animation: fadeInUp 1.2s ease forwards;
  animation-delay: 0.7s;
}

/* Animation for A */
.loading-letter:nth-child(5) {
  animation: fadeInUp 1.2s ease forwards;
  animation-delay: 0.9s;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Restart animation when container becomes active again */
.loading-container.active .loading-letter:nth-child(1) {
  animation: fadeInUp 1.2s ease forwards;
  animation-delay: 0.1s;
}

.loading-container.active .loading-letter:nth-child(2) {
  animation: fadeInUp 1.2s ease forwards;
  animation-delay: 0.3s;
}

.loading-container.active .loading-letter:nth-child(3) {
  animation: fadeInUp 1.2s ease forwards;
  animation-delay: 0.5s;
}

.loading-container.active .loading-letter:nth-child(4) {
  animation: fadeInUp 1.2s ease forwards;
  animation-delay: 0.7s;
}

.loading-container.active .loading-letter:nth-child(5) {
  animation: fadeInUp 1.2s ease forwards;
  animation-delay: 0.9s;
}
