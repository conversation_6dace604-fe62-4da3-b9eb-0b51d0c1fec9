<!DOCTYPE html>
<html>
<head>
    <title>Leaflet Test Map</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="assets/leaflet/leaflet.css" />
    
    <style>
        body {
            margin: 0;
            padding: 0;
        }
        #map {
            width: 100%;
            height: 100vh;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    
    <!-- Leaflet JavaScript -->
    <script src="assets/leaflet/leaflet.js"></script>
    
    <script>
        // Initialize the map
        var map = L.map('map').setView([31.7917, -7.0926], 5);
        
        // Add a simple background color
        document.getElementById('map').style.backgroundColor = '#333';
        
        // Create a simple polygon for Morocco
        var moroccoCoords = [
            [[35.76, -5.81], [35.12, -2.85], [32.52, -1.47], [31.73, -1.16], 
             [30.25, -2.85], [29.23, -8.66], [27.31, -13.12], [23.72, -15.96], 
             [21.42, -17.02], [21.40, -14.84], [27.66, -8.81], [27.67, -4.27], 
             [30.97, -5.54], [35.76, -5.81]]
        ];
        
        // Add the polygon to the map
        var morocco = L.polygon(moroccoCoords, {
            color: 'white',
            fillColor: '#1e90ff',
            fillOpacity: 0.5,
            weight: 2
        }).addTo(map);
        
        // Add a popup
        morocco.bindPopup("Morocco");
        
        // Fit the map to the polygon bounds
        map.fitBounds(morocco.getBounds());
        
        // Log to console for debugging
        console.log('Map initialized');
        console.log('Leaflet version:', L.version);
    </script>
</body>
</html>
