2025-05-13T02:06:58.833Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-13T02:07:08.838Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-13T02:07:18.842Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-13T02:07:28.855Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-13T02:07:38.855Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-13T02:07:48.872Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-13T02:08:32.348Z: Failed to connect to the database: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-13T02:08:32.797Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-13T02:08:32.992Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-13T02:08:42.936Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-13T02:08:52.938Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-15T08:25:37.633Z: Failed to connect to the database: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-15T08:25:38.269Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-15T08:25:38.382Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-15T08:25:40.505Z: Error fetching drone deployments: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-15T08:25:40.510Z: Error in direct drone query: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-15T08:25:40.511Z: Fallback query also failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-15T08:25:40.512Z: Error in get-drone-deployments handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-15T08:25:48.235Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-15T08:25:58.236Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-15T08:26:08.238Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-15T08:26:18.243Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-16T12:28:29.044Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-16T12:28:33.206Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-16T12:28:34.035Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-16T12:28:35.021Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-16T12:28:39.041Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:23:41.025Z: Failed to connect to the database: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:23:41.743Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:23:41.777Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:23:41.843Z: Error fetching current training schedules: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:23:41.848Z: Error in get-training-schedules handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:23:41.945Z: Error fetching activities timeline data: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:23:41.954Z: Error in get-activities-timeline handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:23:51.720Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:24:01.722Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:01.379Z: Failed to connect to the database: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:01.809Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:01.874Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:01.923Z: Error fetching current training schedules: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:01.927Z: Error in get-training-schedules handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:01.972Z: Error fetching activities timeline data: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:01.974Z: Error in get-activities-timeline handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:03.413Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:04.988Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:07.727Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:07.898Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:08.551Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:08.706Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:08.864Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:09.008Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:09.156Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:09.318Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:09.461Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:09.610Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:09.834Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:10.027Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-17T08:25:11.747Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-18T23:13:24.419Z: Failed to connect to the database: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-18T23:13:25.101Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-18T23:13:25.174Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-18T23:13:25.194Z: Error fetching current training schedules: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-18T23:13:25.196Z: Error in get-training-schedules handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-18T23:13:25.248Z: Error fetching activities timeline data: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-18T23:13:25.250Z: Error in get-activities-timeline handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-18T23:13:35.098Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-18T23:13:45.103Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-18T23:50:05.625Z: Error fetching current training schedules: {}
2025-05-18T23:50:05.628Z: Error in get-training-schedules handler: {}
2025-05-18T23:50:10.770Z: Error fetching current training schedules: {}
2025-05-18T23:50:10.773Z: Error in get-training-schedules handler: {}
2025-05-19T13:08:48.346Z: Failed to connect to the database: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T13:08:49.357Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T13:08:49.527Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T13:08:49.576Z: Error fetching current training schedules: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T13:08:49.584Z: Error in get-training-schedules handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T13:08:49.673Z: Error fetching activities timeline data: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T13:08:49.677Z: Error in get-activities-timeline handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T13:08:51.493Z: Error fetching statistics: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T13:08:51.494Z: Error in get-statistics handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T13:08:59.318Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T14:07:19.234Z: Error fetching statistics: {}
2025-05-19T14:07:19.238Z: Error in get-statistics handler: {}
2025-05-19T14:09:13.491Z: Error fetching statistics: {}
2025-05-19T14:09:13.493Z: Error in get-statistics handler: {}
2025-05-19T19:40:40.449Z: Failed to connect to the database: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T19:40:40.907Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T19:40:41.067Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T19:40:41.084Z: Error fetching current training schedules: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T19:40:41.086Z: Error in get-training-schedules handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T19:40:41.122Z: Error fetching activities timeline data: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T19:40:41.123Z: Error in get-activities-timeline handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T19:40:43.063Z: Error fetching statistics: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T19:40:43.064Z: Error in get-statistics handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T19:40:50.896Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-19T19:41:00.900Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-21T16:08:15.988Z: Failed to connect to the database: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-21T16:08:16.800Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-21T16:08:17.144Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-21T16:08:17.172Z: Error fetching current training schedules: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-21T16:08:17.176Z: Error in get-training-schedules handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-21T16:08:17.245Z: Error fetching activities timeline data: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-21T16:08:17.252Z: Error in get-activities-timeline handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-21T16:08:19.125Z: Error fetching statistics: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-21T16:08:19.128Z: Error in get-statistics handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-21T16:08:26.778Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-21T16:08:36.784Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-21T16:14:28.495Z: Error fetching statistics: {}
2025-05-21T16:14:28.502Z: Error in get-statistics handler: {}
2025-05-21T16:14:36.367Z: Error fetching statistics: {}
2025-05-21T16:14:36.372Z: Error in get-statistics handler: {}
2025-05-21T16:18:36.285Z: Error fetching statistics: {
  "message": "Unknown column 'date_debut' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(*) as count\n        FROM schedule_trainings\n        WHERE YEAR(date_debut) = 2025\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'where clause'"
}
2025-05-21T16:18:36.300Z: Error in get-statistics handler: {
  "message": "Unknown column 'date_debut' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(*) as count\n        FROM schedule_trainings\n        WHERE YEAR(date_debut) = 2025\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'where clause'"
}
2025-05-22T11:12:00.312Z: Failed to connect to the database: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-22T11:12:00.826Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-22T11:12:00.981Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-22T11:12:01.000Z: Error fetching current training schedules: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-22T11:12:01.003Z: Error in get-training-schedules handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-22T11:12:01.066Z: Error fetching activities timeline data: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-22T11:12:01.067Z: Error in get-activities-timeline handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-22T11:12:02.979Z: Error fetching statistics: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-22T11:12:02.980Z: Error in get-statistics handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-22T11:12:10.815Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-22T11:25:32.275Z: Error fetching statistics: {
  "message": "Can't add new command when connection is in closed state"
}
2025-05-22T11:25:32.279Z: Error in get-statistics handler: {
  "message": "Can't add new command when connection is in closed state"
}
2025-05-22T14:01:43.644Z: Error fetching instances for training: sdlmsd ksdlksd lsd {
  "message": "Unknown column 'lieu' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          id,\n          nom_formation,\n          du,\n          au,\n          extended_to,\n          rescheduled_du,\n          rescheduled_au,\n          lieu,\n          formateur,\n          nb_participants,\n          commentaires\n        FROM\n          schedule_trainings\n        WHERE\n          nom_formation = 'sdlmsd ksdlksd lsd'\n        ORDER BY\n          CASE\n            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du\n            ELSE du\n          END DESC\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'lieu' in 'field list'"
}
2025-05-22T14:01:45.248Z: Error fetching instances for training: THUNDER-B {
  "message": "Unknown column 'lieu' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          id,\n          nom_formation,\n          du,\n          au,\n          extended_to,\n          rescheduled_du,\n          rescheduled_au,\n          lieu,\n          formateur,\n          nb_participants,\n          commentaires\n        FROM\n          schedule_trainings\n        WHERE\n          nom_formation = 'THUNDER-B'\n        ORDER BY\n          CASE\n            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du\n            ELSE du\n          END DESC\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'lieu' in 'field list'"
}
2025-05-22T14:01:47.280Z: Error fetching instances for training: Wander-B {
  "message": "Unknown column 'lieu' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          id,\n          nom_formation,\n          du,\n          au,\n          extended_to,\n          rescheduled_du,\n          rescheduled_au,\n          lieu,\n          formateur,\n          nb_participants,\n          commentaires\n        FROM\n          schedule_trainings\n        WHERE\n          nom_formation = 'Wander-B'\n        ORDER BY\n          CASE\n            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du\n            ELSE du\n          END DESC\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'lieu' in 'field list'"
}
2025-05-22T14:01:48.399Z: Error fetching instances for training: sdlmsd ksdlksd lsd {
  "message": "Unknown column 'lieu' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          id,\n          nom_formation,\n          du,\n          au,\n          extended_to,\n          rescheduled_du,\n          rescheduled_au,\n          lieu,\n          formateur,\n          nb_participants,\n          commentaires\n        FROM\n          schedule_trainings\n        WHERE\n          nom_formation = 'sdlmsd ksdlksd lsd'\n        ORDER BY\n          CASE\n            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du\n            ELSE du\n          END DESC\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'lieu' in 'field list'"
}
2025-05-22T14:01:50.231Z: Error fetching instances for training: sdlmsd ksdlksd lsd {
  "message": "Unknown column 'lieu' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          id,\n          nom_formation,\n          du,\n          au,\n          extended_to,\n          rescheduled_du,\n          rescheduled_au,\n          lieu,\n          formateur,\n          nb_participants,\n          commentaires\n        FROM\n          schedule_trainings\n        WHERE\n          nom_formation = 'sdlmsd ksdlksd lsd'\n        ORDER BY\n          CASE\n            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du\n            ELSE du\n          END DESC\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'lieu' in 'field list'"
}
2025-05-22T14:01:54.020Z: Error fetching instances for training: sdlmsd ksdlksd lsd {
  "message": "Unknown column 'lieu' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          id,\n          nom_formation,\n          du,\n          au,\n          extended_to,\n          rescheduled_du,\n          rescheduled_au,\n          lieu,\n          formateur,\n          nb_participants,\n          commentaires\n        FROM\n          schedule_trainings\n        WHERE\n          nom_formation = 'sdlmsd ksdlksd lsd'\n        ORDER BY\n          CASE\n            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du\n            ELSE du\n          END DESC\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'lieu' in 'field list'"
}
2025-05-22T14:01:56.288Z: Error fetching instances for training: sdlmsd ksdlksd lsd {
  "message": "Unknown column 'lieu' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          id,\n          nom_formation,\n          du,\n          au,\n          extended_to,\n          rescheduled_du,\n          rescheduled_au,\n          lieu,\n          formateur,\n          nb_participants,\n          commentaires\n        FROM\n          schedule_trainings\n        WHERE\n          nom_formation = 'sdlmsd ksdlksd lsd'\n        ORDER BY\n          CASE\n            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du\n            ELSE du\n          END DESC\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'lieu' in 'field list'"
}
2025-05-22T14:02:28.621Z: Error fetching instances for training: THUNDER-B {
  "message": "Unknown column 'lieu' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          id,\n          nom_formation,\n          du,\n          au,\n          extended_to,\n          rescheduled_du,\n          rescheduled_au,\n          lieu,\n          formateur,\n          nb_participants,\n          commentaires\n        FROM\n          schedule_trainings\n        WHERE\n          nom_formation = 'THUNDER-B'\n        ORDER BY\n          CASE\n            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du\n            ELSE du\n          END DESC\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'lieu' in 'field list'"
}
2025-05-22T14:03:34.743Z: Error fetching instances for training: Wander-B {
  "message": "Unknown column 'lieu' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          id,\n          nom_formation,\n          du,\n          au,\n          extended_to,\n          rescheduled_du,\n          rescheduled_au,\n          lieu,\n          formateur,\n          nb_participants,\n          commentaires\n        FROM\n          schedule_trainings\n        WHERE\n          nom_formation = 'Wander-B'\n        ORDER BY\n          CASE\n            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du\n            ELSE du\n          END DESC\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'lieu' in 'field list'"
}
2025-05-22T14:03:36.541Z: Error fetching instances for training: sdlmsd ksdlksd lsd {
  "message": "Unknown column 'lieu' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          id,\n          nom_formation,\n          du,\n          au,\n          extended_to,\n          rescheduled_du,\n          rescheduled_au,\n          lieu,\n          formateur,\n          nb_participants,\n          commentaires\n        FROM\n          schedule_trainings\n        WHERE\n          nom_formation = 'sdlmsd ksdlksd lsd'\n        ORDER BY\n          CASE\n            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du\n            ELSE du\n          END DESC\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'lieu' in 'field list'"
}
2025-05-22T14:04:08.539Z: Error fetching instances for training: THUNDER-B {
  "message": "Unknown column 'lieu' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          id,\n          nom_formation,\n          du,\n          au,\n          extended_to,\n          rescheduled_du,\n          rescheduled_au,\n          lieu,\n          formateur,\n          nb_participants,\n          commentaires\n        FROM\n          schedule_trainings\n        WHERE\n          nom_formation = 'THUNDER-B'\n        ORDER BY\n          CASE\n            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du\n            ELSE du\n          END DESC\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'lieu' in 'field list'"
}
2025-05-22T14:04:09.831Z: Error fetching instances for training: Wander-B {
  "message": "Unknown column 'lieu' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          id,\n          nom_formation,\n          du,\n          au,\n          extended_to,\n          rescheduled_du,\n          rescheduled_au,\n          lieu,\n          formateur,\n          nb_participants,\n          commentaires\n        FROM\n          schedule_trainings\n        WHERE\n          nom_formation = 'Wander-B'\n        ORDER BY\n          CASE\n            WHEN rescheduled_du IS NOT NULL THEN rescheduled_du\n            ELSE du\n          END DESC\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'lieu' in 'field list'"
}
2025-05-23T16:00:12.109Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:12.116Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:17.957Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:17.961Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:18.875Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:18.878Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:32.732Z: Error fetching total trainees for training MISS MISS: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'MISS MISS'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:32.736Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'MISS MISS'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:33.447Z: Error fetching total trainees for training sdlmsd ksdlksd lsd: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:33.451Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:35.003Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:35.005Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:36.201Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:36.210Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:36.962Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:00:36.964Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:22.689Z: Error fetching total trainees for training Certification PHANTOM 8: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:22.695Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:22.884Z: Error fetching total trainees for training Certification PHANTOM 8: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:22.887Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:23.168Z: Error fetching total trainees for training Certification PHANTOM 8: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:23.170Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:23.336Z: Error fetching total trainees for training Certification PHANTOM 8: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:23.339Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:23.524Z: Error fetching total trainees for training Certification PHANTOM 8: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:23.527Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:23.669Z: Error fetching total trainees for training Certification PHANTOM 8: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:23.671Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:23.795Z: Error fetching total trainees for training Certification PHANTOM 8: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:23.797Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:23.965Z: Error fetching total trainees for training Certification PHANTOM 8: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:23.968Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:24.415Z: Error fetching total trainees for training Certification PHANTOM 8: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:24.420Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:24.714Z: Error fetching total trainees for training Certification PHANTOM 8: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:24.717Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Certification PHANTOM 8'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:25.748Z: Error fetching total trainees for training MISS MISS: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'MISS MISS'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:25.750Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'MISS MISS'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:27.888Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:27.889Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:28.738Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:28.739Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:29.108Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:29.111Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:29.497Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:29.503Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:30.087Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:30.088Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:30.569Z: Error fetching total trainees for training sdlmsd ksdlksd lsd: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:30.573Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:31.387Z: Error fetching total trainees for training MISS MISS: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'MISS MISS'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:31.390Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'MISS MISS'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:37.181Z: Error fetching total trainees for training HAHAHAHAHAHAHAHAH: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'HAHAHAHAHAHAHAHAH'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:37.182Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'HAHAHAHAHAHAHAHAH'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:38.906Z: Error fetching total trainees for training sdlmsd ksdlksd lsd: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:38.907Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:39.714Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:39.716Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:40.393Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:40.395Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:41.350Z: Error fetching total trainees for training sdlmsd ksdlksd lsd: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:41.356Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:41.702Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:41.708Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:42.458Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:42.459Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:43.818Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:43.819Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:43.989Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:43.991Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:44.370Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:44.375Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:44.733Z: Error fetching total trainees for training sdlmsd ksdlksd lsd: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:44.734Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:45.033Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:45.034Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:45.277Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:45.280Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:45.625Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:02:45.633Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:03:17.880Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:03:17.881Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:03:19.777Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:03:19.778Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:03:34.265Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:03:34.266Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:03:44.553Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:03:44.554Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:04:17.334Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:04:17.336Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:04:19.127Z: Error fetching total trainees for training sdlmsd ksdlksd lsd: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:04:19.129Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'ag.nom_formation' in 'where clause'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT COUNT(DISTINCT s.id) as total_trainees\n        FROM stagiaires s\n        INNER JOIN activites_gsa ag ON s.formation_id = ag.id\n        WHERE ag.nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'ag.nom_formation' in 'where clause'"
}
2025-05-23T16:06:27.363Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:06:27.365Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:06:29.193Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:06:29.194Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:06:30.143Z: Error fetching total trainees for training sdlmsd ksdlksd lsd: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:06:30.144Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:07:21.803Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:07:21.804Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:07:22.407Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:07:22.407Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:07:22.842Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:07:22.845Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:07:58.607Z: Error fetching total trainees for training MISS MISS: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'MISS MISS'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:07:58.608Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'MISS MISS'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:08:25.751Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:08:25.752Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:08:26.720Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:08:26.721Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:08:27.145Z: Error fetching total trainees for training sdlmsd ksdlksd lsd: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:08:27.146Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'sdlmsd ksdlksd lsd'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:08:27.515Z: Error fetching total trainees for training HAHAHAHAHAHAHAHAH: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'HAHAHAHAHAHAHAHAH'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:08:27.517Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'HAHAHAHAHAHAHAHAH'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:08:28.229Z: Error fetching total trainees for training THUNDER-B: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:08:28.231Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'THUNDER-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:08:28.687Z: Error fetching total trainees for training Wander-B: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-23T16:08:28.688Z: Error in get-total-trainees-for-training-name handler: {
  "message": "Unknown column 'nom_formation' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT id, nom_formation\n        FROM activites_gsa\n        WHERE nom_formation = 'Wander-B'\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'nom_formation' in 'field list'"
}
2025-05-24T15:02:54.881Z: Failed to connect to the database: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-24T15:02:55.729Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-24T15:02:56.037Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-24T15:02:56.055Z: Error fetching current training schedules: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-24T15:02:56.062Z: Error in get-training-schedules handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-24T15:02:56.162Z: Error fetching activities timeline data: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-24T15:02:56.167Z: Error in get-activities-timeline handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-24T15:02:58.038Z: Error fetching statistics: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-24T15:02:58.042Z: Error in get-statistics handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-24T15:03:05.679Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-24T15:03:15.687Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-24T15:03:25.701Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-24T16:50:16.682Z: Error fetching all trainees for training Wander-B: {
  "message": "Unknown column 'st.au_extended' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n        SELECT\n          s.id,\n          s.grade,\n          s.nom,\n          s.prenom,\n          s.mle,\n          s.unite,\n          s.fonction,\n          s.formation_id,\n          st.tranche,\n          st.du,\n          st.au,\n          st.au_extended,\n          st.rescheduled\n        FROM\n          stagiaires s\n        LEFT JOIN\n          schedule_trainings st ON s.formation_id = st.id\n        WHERE\n          s.formation_id IN (1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,43,44)\n        ORDER BY\n          s.formation_id, s.fonction, s.grade, s.nom, s.prenom\n      ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'st.au_extended' in 'field list'"
}
2025-05-25T01:17:09.664Z: Error fetching statistics: {
  "message": "Can't add new command when connection is in closed state"
}
2025-05-25T01:17:09.670Z: Error in get-statistics handler: {
  "message": "Can't add new command when connection is in closed state"
}
2025-05-25T11:54:40.771Z: Error fetching training statistics for domain 'Commande and controle': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Commande and controle' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:40.774Z: Error in get-training-statistics-by-domain handler for domain 'Commande and controle': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Commande and controle' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:41.409Z: Error fetching training statistics for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:41.411Z: Error in get-training-statistics-by-domain handler for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:42.084Z: Error fetching training statistics for domain 'Missiles': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Missiles' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:42.087Z: Error in get-training-statistics-by-domain handler for domain 'Missiles': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Missiles' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:42.703Z: Error fetching training statistics for domain 'SIG': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'SIG' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:42.704Z: Error in get-training-statistics-by-domain handler for domain 'SIG': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'SIG' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:43.350Z: Error fetching training statistics for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:43.351Z: Error in get-training-statistics-by-domain handler for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:44.255Z: Error fetching training statistics for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:44.257Z: Error in get-training-statistics-by-domain handler for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:44.928Z: Error fetching training statistics for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:44.931Z: Error in get-training-statistics-by-domain handler for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:45.281Z: Error fetching training statistics for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:45.283Z: Error in get-training-statistics-by-domain handler for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:45.956Z: Error fetching training statistics for domain 'Missiles': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Missiles' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:45.968Z: Error in get-training-statistics-by-domain handler for domain 'Missiles': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Missiles' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:46.629Z: Error fetching training statistics for domain 'Commande and controle': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Commande and controle' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:46.633Z: Error in get-training-statistics-by-domain handler for domain 'Commande and controle': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Commande and controle' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:47.374Z: Error fetching training statistics for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:47.378Z: Error in get-training-statistics-by-domain handler for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:47.864Z: Error fetching training statistics for domain 'Missiles': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Missiles' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:47.865Z: Error in get-training-statistics-by-domain handler for domain 'Missiles': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Missiles' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:48.250Z: Error fetching training statistics for domain 'SIG': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'SIG' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:48.255Z: Error in get-training-statistics-by-domain handler for domain 'SIG': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'SIG' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:52.561Z: Error fetching training statistics for domain 'Commande and controle': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Commande and controle' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:52.567Z: Error in get-training-statistics-by-domain handler for domain 'Commande and controle': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Commande and controle' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:53.455Z: Error fetching training statistics for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:53.457Z: Error in get-training-statistics-by-domain handler for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:54.949Z: Error fetching training statistics for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:54:54.951Z: Error in get-training-statistics-by-domain handler for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:03.631Z: Error fetching training statistics for domain 'Missiles': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Missiles' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:03.641Z: Error in get-training-statistics-by-domain handler for domain 'Missiles': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Missiles' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:04.230Z: Error fetching training statistics for domain 'SIG': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'SIG' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:04.233Z: Error in get-training-statistics-by-domain handler for domain 'SIG': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'SIG' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:05.456Z: Error fetching training statistics for domain 'Commande and controle': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Commande and controle' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:05.461Z: Error in get-training-statistics-by-domain handler for domain 'Commande and controle': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Commande and controle' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:47.210Z: Error fetching training statistics for domain 'Commande and controle': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Commande and controle' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:47.214Z: Error in get-training-statistics-by-domain handler for domain 'Commande and controle': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Commande and controle' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:48.299Z: Error fetching training statistics for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:48.302Z: Error in get-training-statistics-by-domain handler for domain 'drone': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'drone' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:48.994Z: Error fetching training statistics for domain 'Missiles': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Missiles' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:48.996Z: Error in get-training-statistics-by-domain handler for domain 'Missiles': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'Missiles' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:49.757Z: Error fetching training statistics for domain 'SIG': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'SIG' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-25T11:55:49.759Z: Error in get-training-statistics-by-domain handler for domain 'SIG': {
  "message": "Unknown column 'date_debut' in 'field list'",
  "code": "ER_BAD_FIELD_ERROR",
  "errno": 1054,
  "sql": "\n          SELECT YEAR(date_debut) as year, COUNT(*) as count\n          FROM schedule_trainings\n          WHERE domaine = 'SIG' AND date_debut IS NOT NULL\n          GROUP BY YEAR(date_debut)\n          ORDER BY year\n        ",
  "sqlState": "42S22",
  "sqlMessage": "Unknown column 'date_debut' in 'field list'"
}
2025-05-30T08:31:58.426Z: Failed to connect to the database: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T08:32:00.024Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T08:32:00.497Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T08:32:00.530Z: Error fetching current training schedules: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T08:32:00.532Z: Error in get-training-schedules handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T08:32:00.823Z: Error fetching activities timeline data: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T08:32:00.837Z: Error in get-activities-timeline handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T14:35:34.556Z: Failed to connect to the database: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T14:35:35.325Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T14:35:35.639Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T14:35:35.655Z: Error fetching current training schedules: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T14:35:35.657Z: Error in get-training-schedules handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T14:35:35.739Z: Error fetching activities timeline data: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T14:35:35.746Z: Error in get-activities-timeline handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T14:35:37.637Z: Error fetching statistics: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T14:35:37.640Z: Error in get-statistics handler: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T14:35:45.296Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T14:35:55.305Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
2025-05-30T14:36:05.311Z: Database connection check failed: {
  "code": "ECONNREFUSED",
  "fatal": true
}
