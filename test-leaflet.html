<!DOCTYPE html>
<html>
<head>
    <title>Leaflet Test</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="assets/leaflet/leaflet.css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #1e1e1e;
            color: #cccccc;
            font-family: Arial, sans-serif;
        }
        
        .container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            color: #cccccc;
            text-align: center;
        }
        
        #map {
            height: 500px;
            width: 100%;
            border: 1px solid #444;
            border-radius: 5px;
        }
        
        .info {
            margin-top: 20px;
            padding: 10px;
            background-color: #333;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Leaflet Map Test</h1>
        
        <div id="map"></div>
        
        <div class="info">
            <p>This is a test page for Leaflet map functionality.</p>
        </div>
    </div>
    
    <!-- Leaflet JavaScript -->
    <script src="assets/leaflet/leaflet.js"></script>
    
    <script>
        // Initialize the map
        console.log('Initializing map...');
        
        // Create the map
        var map = L.map('map').setView([31.7917, -7.0926], 5);
        
        console.log('Map created');
        
        // Set background color
        document.getElementById('map').style.backgroundColor = '#333';
        
        // Create a simple polygon for Morocco
        var moroccoCoords = [
            [
                [35.76, -5.81], [35.12, -2.85], [32.52, -1.47], [31.73, -1.16], 
                [30.25, -2.85], [29.23, -8.66], [27.31, -13.12], [23.72, -15.96], 
                [21.42, -17.02], [21.40, -14.84], [27.66, -8.81], [27.67, -4.27], 
                [30.97, -5.54], [35.76, -5.81]
            ]
        ];
        
        // Add the polygon to the map
        var morocco = L.polygon(moroccoCoords, {
            color: 'white',
            fillColor: '#1e90ff',
            fillOpacity: 0.5,
            weight: 2
        }).addTo(map);
        
        // Add a popup
        morocco.bindPopup("Morocco");
        
        // Fit the map to the polygon bounds
        map.fitBounds(morocco.getBounds());
        
        console.log('Morocco polygon added to map');
    </script>
</body>
</html>
