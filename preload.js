// Preload script
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('api', {
  // Database functions
  checkDatabaseConnection: async () => {
    return await ipcRenderer.invoke('check-database-connection');
  },

  // Get drone deployments
  getDroneDeployments: async () => {
    return await ipcRenderer.invoke('get-drone-deployments');
  },

  // Get missile deployments
  getMissileDeployments: async () => {
    return await ipcRenderer.invoke('get-missile-deployments');
  },

  // Get training schedules
  getTrainingSchedules: async () => {
    return await ipcRenderer.invoke('get-training-schedules');
  },

  // Get activities timeline data
  getActivitiesTimeline: async () => {
    return await ipcRenderer.invoke('get-activities-timeline');
  },

  // Get statistics for KPI dashboard
  getStatistics: async () => {
    return await ipc<PERSON>enderer.invoke('get-statistics');
  },

  // Get training statistics for a specific domain
  getTrainingStatisticsByDomain: async (domain) => {
    return await ipcRenderer.invoke('get-training-statistics-by-domain', domain);
  },

  // Get trainee statistics for a specific domain
  getTraineeStatisticsByDomain: async (domain) => {
    return await ipcRenderer.invoke('get-trainee-statistics-by-domain', domain);
  },

  // Get trainees by domain
  getTraineesByDomain: async (domain) => {
    return await ipcRenderer.invoke('get-trainees-by-domain', domain);
  },

  // Get formations by domain with last instance dates
  getFormationsByDomain: async (domain) => {
    return await ipcRenderer.invoke('get-formations-by-domain', domain);
  },

  // Get unique trainings (without duplicates)
  getUniqueTrainings: async (domaine) => {
    return await ipcRenderer.invoke('get-unique-trainings', domaine);
  },

  // Get training details
  getTrainingDetails: async (nomFormation) => {
    return await ipcRenderer.invoke('get-training-details', nomFormation);
  },

  // Get all instances of a training
  getTrainingInstances: async (nomFormation) => {
    return await ipcRenderer.invoke('get-training-instances', nomFormation);
  },

  // Get trainees for a specific training instance
  getTraineesForTraining: async (trainingId) => {
    return await ipcRenderer.invoke('get-trainees-for-training', trainingId);
  },

  // Get radied trainees for a specific training instance
  getRadiedTrainees: async (trainingId) => {
    return await ipcRenderer.invoke('get-radied-trainees', trainingId);
  },

  // Get total number of trainees for all instances of a training
  getTotalTraineesForTrainingName: async (trainingName) => {
    return await ipcRenderer.invoke('get-total-trainees-for-training-name', trainingName);
  },

  // Get total number of radied trainees for all instances of a training
  getTotalRadiedTraineesForTrainingName: async (trainingName) => {
    return await ipcRenderer.invoke('get-total-radied-trainees-for-training-name', trainingName);
  },

  // Get all trainees for all instances of a training
  getAllTraineesForTraining: async (trainingName) => {
    return await ipcRenderer.invoke('get-all-trainees-for-training', trainingName);
  },

  // Add more IPC communication methods as needed
  send: (channel, data) => {
    // Whitelist channels
    const validChannels = ['app-ready'];
    if (validChannels.includes(channel)) {
      ipcRenderer.send(channel, data);
    }
  },

  receive: (channel, func) => {
    // Whitelist channels
    const validChannels = ['database-status'];
    if (validChannels.includes(channel)) {
      // Deliberately strip event as it includes `sender`
      ipcRenderer.on(channel, (event, ...args) => func(...args));
    }
  },

  // Expose console for debugging
  console: {
    log: (...args) => console.log(...args),
    error: (...args) => console.error(...args),
    warn: (...args) => console.warn(...args),
    info: (...args) => console.info(...args),
  },
});
