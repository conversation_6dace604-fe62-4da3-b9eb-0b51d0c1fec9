<!DOCTYPE html>
<html>
<head>
    <title>Simple Morocco Map</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #1e1e1e;
            color: #cccccc;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            padding: 20px;
        }
        
        h1 {
            margin-top: 0;
            color: #888888;
            font-size: 12px;
            text-align: right;
        }
        
        #map {
            flex: 1;
            border-radius: 6px;
            border: 1px solid #333333;
        }
        
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Morocco Map</h1>
        <div id="map"></div>
        <div class="footer">
            This is a simple Morocco map that works offline
        </div>
    </div>
    
    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // Initialize the map
        var map = L.map('map').setView([31.7917, -7.0926], 5);
        
        // Create a simple polygon for Morocco
        var moroccoCoords = [
            [[35.76, -5.81], [35.12, -2.85], [32.52, -1.47], [31.73, -1.16], 
             [30.25, -2.85], [29.23, -8.66], [27.31, -13.12], [23.72, -15.96], 
             [21.42, -17.02], [21.40, -14.84], [27.66, -8.81], [27.67, -4.27], 
             [30.97, -5.54], [35.76, -5.81]]
        ];
        
        // Add the polygon to the map
        var morocco = L.polygon(moroccoCoords, {
            color: 'white',
            fillColor: '#1e90ff', // dodgerblue
            fillOpacity: 0.5,
            weight: 2
        }).addTo(map);
        
        // Add a popup
        morocco.bindPopup("Morocco");
        
        // Fit the map to the polygon bounds
        map.fitBounds(morocco.getBounds());
        
        // Log to console for debugging
        console.log('Map initialized');
    </script>
</body>
</html>
