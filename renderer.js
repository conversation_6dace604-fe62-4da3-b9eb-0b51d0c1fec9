// Variables globales pour la gestion des formations
let currentTrainingData = null;
let currentInstancesData = null;
let currentSelectedInstance = null;
let scrollObserver = null;

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
  // Use the exposed console API for debugging
  if (window.api && window.api.console) {
    window.api.console.log('Renderer process started');
  }

  // Loading indicator functions
  window.showLoading = function(container) {
    // Clone the loading indicator template
    const template = document.getElementById('loading-indicator-template');
    const loadingIndicator = template.content.cloneNode(true);

    // Get the container element
    const targetContainer = typeof container === 'string'
      ? document.querySelector(container)
      : container;

    if (!targetContainer) {
      console.error('Loading indicator target container not found:', container);
      return null;
    }

    // Make sure the container has position relative or absolute
    const computedStyle = window.getComputedStyle(targetContainer);
    if (computedStyle.position === 'static') {
      targetContainer.style.position = 'relative';
    }

    // Append the loading indicator to the container
    targetContainer.appendChild(loadingIndicator);

    // Get the loading container element
    const loadingContainer = targetContainer.querySelector('.loading-container');

    // Activate the loading indicator (triggers animation)
    setTimeout(() => {
      loadingContainer.classList.add('active');
    }, 10);

    return loadingContainer;
  };

  window.hideLoading = function(container) {
    // Get the container element
    const targetContainer = typeof container === 'string'
      ? document.querySelector(container)
      : container;

    if (!targetContainer) {
      console.error('Loading indicator target container not found:', container);
      return;
    }

    // Get the loading container element
    const loadingContainer = targetContainer.querySelector('.loading-container');

    if (!loadingContainer) {
      console.error('Loading container not found in target container');
      return;
    }

    // Deactivate the loading indicator
    loadingContainer.classList.remove('active');

    // Remove the loading indicator after animation completes
    setTimeout(() => {
      loadingContainer.remove();
    }, 300);
  };
  // Get all tabs and tab content elements
  const tabs = document.querySelectorAll('.tab');
  const tabContents = document.querySelectorAll('.tab-content');
  const themeSwitcher = document.getElementById('theme-switcher');
  const datetimeDisplay = document.getElementById('datetime-display');
  const dbErrorScreen = document.getElementById('db-error-screen');
  const dbErrorMessage = document.getElementById('db-error-message');
  const retryConnectionBtn = document.getElementById('retry-connection');
  const appContainer = document.getElementById('app-container');

  // Database connection handling
  let dbConnected = false;

  // Listen for database status updates from main process
  window.api.receive('database-status', (status) => {
    dbConnected = status.connected;

    if (dbConnected) {
      // Hide error screen and show app
      dbErrorScreen.classList.remove('active');
      appContainer.style.display = 'flex';
    } else {
      // Show error screen and hide app
      dbErrorScreen.classList.add('active');
      appContainer.style.display = 'none';

      // Update error message if available
      if (status.error) {
        dbErrorMessage.textContent = `Erreur: ${status.error}`;
      } else {
        dbErrorMessage.textContent =
          'Impossible de se connecter au serveur de base de données.';
      }
    }
  });

  // Retry connection button
  retryConnectionBtn.addEventListener('click', async () => {
    // Show loading state
    retryConnectionBtn.textContent = 'Connexion en cours...';
    retryConnectionBtn.disabled = true;

    // Check database connection
    const status = await window.api.checkDatabaseConnection();

    // Reset button
    retryConnectionBtn.textContent = 'Réessayer';
    retryConnectionBtn.disabled = false;

    // Update UI based on connection status
    if (status.connected) {
      dbErrorScreen.classList.remove('active');
      appContainer.style.display = 'flex';
    } else {
      dbErrorMessage.textContent = status.error
        ? `Erreur: ${status.error}`
        : 'Impossible de se connecter au serveur de base de données.';
    }
  });

  // Prevent global scrolling from the start
  document.body.style.overflow = 'hidden';
  document.documentElement.style.overflow = 'hidden';

  // Notify main process that renderer is ready
  window.api.send('app-ready');

  // Tabs collapse/expand functionality
  const lateralTabs = document.getElementById('lateral-tabs');
  const tabsToggle = document.getElementById('tabs-toggle');

  // Check for saved tabs state
  const tabsCollapsed = localStorage.getItem('tabs-collapsed') === 'true';
  if (tabsCollapsed) {
    lateralTabs.classList.add('collapsed');
    appContainer.classList.add('tabs-collapsed');
  }

  // Toggle tabs collapsed state
  tabsToggle.addEventListener('click', () => {
    lateralTabs.classList.toggle('collapsed');
    appContainer.classList.toggle('tabs-collapsed');

    // Save state to localStorage
    const isCollapsed = lateralTabs.classList.contains('collapsed');
    localStorage.setItem('tabs-collapsed', isCollapsed);
  });

  // French month names
  const frenchMonths = [
    'janvier',
    'février',
    'mars',
    'avril',
    'mai',
    'juin',
    'juillet',
    'août',
    'septembre',
    'octobre',
    'novembre',
    'décembre',
  ];

  // Function to update date and time in French format
  function updateDateTime() {
    const now = new Date();
    const day = now.getDate();
    const month = frenchMonths[now.getMonth()];
    const year = now.getFullYear();
    const hours = now.getHours();
    const minutes = now.getMinutes().toString().padStart(2, '0');

    // Format: "16 octobre 2025, 22h30"
    const formattedDateTime = `${day} ${month} ${year}, ${hours}h${minutes}`;
    datetimeDisplay.textContent = formattedDateTime;

    // Update the current year display
    const currentYearDisplay = document.getElementById('current-year-display');
    if (currentYearDisplay) {
      currentYearDisplay.textContent = year;
    }
  }

  // Update date and time immediately and then every second
  updateDateTime();
  setInterval(updateDateTime, 1000);

  // Fullscreen toggle functionality
  const fullscreenToggle = document.getElementById('fullscreen-toggle');

  // Check if fullscreen is supported
  const isFullscreenEnabled =
    document.fullscreenEnabled ||
    document.webkitFullscreenEnabled ||
    document.mozFullScreenEnabled ||
    document.msFullscreenEnabled;

  if (isFullscreenEnabled) {
    fullscreenToggle.addEventListener('click', () => {
      const isFullscreen =
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement;

      if (!isFullscreen) {
        // Enter fullscreen
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen();
        } else if (document.documentElement.webkitRequestFullscreen) {
          document.documentElement.webkitRequestFullscreen();
        } else if (document.documentElement.mozRequestFullScreen) {
          document.documentElement.mozRequestFullScreen();
        } else if (document.documentElement.msRequestFullscreen) {
          document.documentElement.msRequestFullscreen();
        }

        // Change icon to compress
        fullscreenToggle.querySelector('i').classList.remove('fa-expand');
        fullscreenToggle.querySelector('i').classList.add('fa-compress');
      } else {
        // Exit fullscreen
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }

        // Change icon to expand
        fullscreenToggle.querySelector('i').classList.remove('fa-compress');
        fullscreenToggle.querySelector('i').classList.add('fa-expand');
      }
    });

    // Listen for fullscreen change events to update icon
    document.addEventListener('fullscreenchange', updateFullscreenIcon);
    document.addEventListener('webkitfullscreenchange', updateFullscreenIcon);
    document.addEventListener('mozfullscreenchange', updateFullscreenIcon);
    document.addEventListener('MSFullscreenChange', updateFullscreenIcon);

    function updateFullscreenIcon() {
      const isFullscreen =
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement;

      const icon = fullscreenToggle.querySelector('i');
      if (isFullscreen) {
        icon.classList.remove('fa-expand');
        icon.classList.add('fa-compress');
      } else {
        icon.classList.remove('fa-compress');
        icon.classList.add('fa-expand');
      }
    }
  } else {
    // Hide the button if fullscreen is not supported
    fullscreenToggle.style.display = 'none';
  }

  // Theme management
  // Check for saved theme preference or default to dark theme
  const savedTheme = localStorage.getItem('theme') || 'dark';
  if (savedTheme === 'light') {
    document.body.classList.add('light-theme');
    themeSwitcher.querySelector('i').classList.remove('fa-sun');
    themeSwitcher.querySelector('i').classList.add('fa-moon');
  }

  // Theme switcher functionality
  themeSwitcher.addEventListener('click', () => {
    document.body.classList.toggle('light-theme');
    const isDarkTheme = !document.body.classList.contains('light-theme');

    // Update icon
    const icon = themeSwitcher.querySelector('i');
    if (isDarkTheme) {
      icon.classList.remove('fa-moon');
      icon.classList.add('fa-sun');
      localStorage.setItem('theme', 'dark');
    } else {
      icon.classList.remove('fa-sun');
      icon.classList.add('fa-moon');
      localStorage.setItem('theme', 'light');
    }
  });

  // Function to switch tabs with animation
  function switchTab(tabName, direction = null) {
    // Get the currently active tab content
    const activeContent = document.querySelector('.tab-content.active');
    const newContent = document.getElementById(`${tabName}-content`);

    // Apply slide animation classes based on direction
    if (direction) {
      if (direction === 'next') {
        activeContent.classList.add('slide-left');
        newContent.classList.add('slide-right');
      } else if (direction === 'prev') {
        activeContent.classList.add('slide-right');
        newContent.classList.add('slide-left');
      }
    }

    // Remove active class from all tabs and tab contents
    tabs.forEach((t) => t.classList.remove('active'));
    tabContents.forEach((content) => content.classList.remove('active'));

    // Add active class to the clicked tab and corresponding content
    const selectedTab = document.querySelector(`.tab[data-tab="${tabName}"]`);
    selectedTab.classList.add('active');
    newContent.classList.add('active', 'slide-in');

    // Special handling for map tab
    if (tabName === 'map') {
      console.log('Map tab activated from switchTab function');
      // Force map initialization if it exists
      if (typeof initMap === 'function') {
        setTimeout(() => {
          console.log('Calling initMap from switchTab');
          initMap();
        }, 500);
      }
    }

    // Prevent body scrolling for all tabs to avoid global scrolling
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // Special handling for list tab
    if (tabName === 'list') {
      console.log('List tab activated from switchTab function');
      // Initialize the resizable panels and load domains
      setTimeout(() => {
        console.log('Initializing list view components');
        loadDomains();
        initResizablePanels();
      }, 500);
    }

    // Special handling for dashboard tab
    if (tabName === 'dashboard') {
      console.log('Dashboard tab activated from switchTab function');
      // Ensure dashboard content is properly contained
      setTimeout(() => {
        console.log('Initializing dashboard view components');
        // Any dashboard-specific initialization can go here
      }, 500);
    }

    // Special handling for actions tab
    if (tabName === 'actions') {
      console.log('Actions tab activated from switchTab function');
      // Ensure actions content is properly contained
      setTimeout(() => {
        console.log('Initializing actions view components');
        // Any actions-specific initialization can go here
      }, 500);
    }

    // Clean up animation classes after transition
    setTimeout(() => {
      tabContents.forEach((content) => {
        content.classList.remove('slide-left', 'slide-right', 'slide-in');
      });
    }, 500);
  }

  // Add click event listeners to each tab
  tabs.forEach((tab) => {
    tab.addEventListener('click', () => {
      const tabName = tab.getAttribute('data-tab');
      switchTab(tabName);
    });
  });

  // Removed scrolling to switch tabs to allow vertical scrolling in dashboard

  // Removed mouse wheel scrolling for tab switching to allow vertical scrolling in dashboard

  // Initialize the planning schedule
  function initPlanningSchedule() {
    const planningSchedule = document.getElementById('planning-schedule');
    if (!planningSchedule) return;

    // Get the control buttons from HTML
    const currentWeekBtn = document.getElementById('current-week-btn');
    const refreshBtn = document.getElementById('refresh-btn');

    // French day names (Monday to Friday only)
    const frenchDays = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven'];

    // Get current date
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentDay = currentDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Calculate the first day of the year
    const firstDayOfYear = new Date(currentYear, 0, 1);

    // Get the day of the week for January 1st (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
    const firstDayOfYearDayOfWeek = firstDayOfYear.getDay();

    // Calculate days to add to get to the first Monday of the year
    let daysToFirstMonday = 1 - firstDayOfYearDayOfWeek; // If Jan 1 is Monday (1), add 0 days
    if (daysToFirstMonday > 1) daysToFirstMonday -= 7; // If Jan 1 is Sat/Sun, go back to previous Monday

    // Calculate the first Monday of the year
    const firstMondayOfYear = new Date(firstDayOfYear);
    firstMondayOfYear.setDate(firstDayOfYear.getDate() + daysToFirstMonday);

    // Calculate the current week number (ISO week number)
    // The formula is: Math.ceil((currentDate - firstMondayOfYear) / (7 * 24 * 60 * 60 * 1000)) + 1
    const currentWeek = Math.ceil((currentDate - firstMondayOfYear) / (7 * 24 * 60 * 60 * 1000));

    console.log(`Current date: ${currentDate.toDateString()}`);
    console.log(`First Monday of year: ${firstMondayOfYear.toDateString()}`);
    console.log(`Current week number: ${currentWeek}`);

    // Calculate the number of weeks in the year
    const lastDayOfYear = new Date(currentYear, 11, 31);
    const numWeeks = Math.ceil((lastDayOfYear - firstDayOfYear) / (7 * 24 * 60 * 60 * 1000));

    // Create timeline container
    const timelineContainer = document.createElement('div');
    timelineContainer.className = 'timeline-container';

    // Create weeks header row
    const timelineWeeks = document.createElement('div');
    timelineWeeks.className = 'timeline-weeks';

    // Create days row
    const timelineDays = document.createElement('div');
    timelineDays.className = 'timeline-days';

    // Create timeline line with ticks
    const timelineLine = document.createElement('div');
    timelineLine.className = 'timeline-line';

    // Create events container
    const timelineEvents = document.createElement('div');
    timelineEvents.className = 'timeline-events';

    // We'll create event rows dynamically based on the number of trainings
    // First, fetch the training schedules
    fetchTrainingSchedules(timelineEvents, firstDayOfYear);

    // Generate weeks and days
    let dayCount = 0;

    for (let week = 1; week <= numWeeks; week++) {
      // Create week header
      const weekHeader = document.createElement('div');
      weekHeader.className = 'week-header';

      // Highlight current week
      if (week === currentWeek) {
        weekHeader.classList.add('current-week');
        weekHeader.textContent = `Semaine actuelle`;
      } else {
        weekHeader.textContent = `Semaine ${week}`;
      }
      timelineWeeks.appendChild(weekHeader);

      // Calculate the first day of the week (Monday)
      const firstDayOfWeek = new Date(firstDayOfYear);

      // Get the day of the week for January 1st (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
      const firstDayOfYearDayOfWeek = firstDayOfYear.getDay();

      // Calculate days to add to get to the first Monday of the year
      let daysToFirstMonday = 1 - firstDayOfYearDayOfWeek; // If Jan 1 is Monday (1), add 0 days
      if (daysToFirstMonday > 1) daysToFirstMonday -= 7; // If Jan 1 is Sat/Sun, go back to previous Monday

      // Set to the first Monday of the year
      firstDayOfWeek.setDate(firstDayOfYear.getDate() + daysToFirstMonday);

      // Now add the weeks
      firstDayOfWeek.setDate(firstDayOfWeek.getDate() + (week - 1) * 7);

      console.log(`Week ${week} starts on: ${firstDayOfWeek.toDateString()}`);

      // Create day columns (Monday to Friday only)
      for (let dayIndex = 0; dayIndex < 5; dayIndex++) {
        const dayDate = new Date(firstDayOfWeek);
        dayDate.setDate(firstDayOfWeek.getDate() + dayIndex);

        // Create day column
        const dayColumn = document.createElement('div');
        dayColumn.className = 'day-column';

        // Check if this is the current day (adjust for weekends)
        const isCurrentDay = (
          week === currentWeek &&
          // Convert Sunday (0) to 7, then subtract 1 to get 0-indexed day of week
          // This makes Monday=0, Tuesday=1, etc.
          (currentDay === 0 ? 6 : currentDay - 1) === dayIndex
        );

        // Highlight current day
        if (isCurrentDay) {
          dayColumn.classList.add('current-day');
        }

        // Create day name
        const dayName = document.createElement('div');
        dayName.className = 'day-name';
        dayName.textContent = frenchDays[dayIndex];
        dayColumn.appendChild(dayName);

        // Create day date
        const dayDateElement = document.createElement('div');
        dayDateElement.className = 'day-date';
        // Format as DD.MM for better readability
        dayDateElement.textContent = `${dayDate.getDate().toString().padStart(2, '0')}.${(dayDate.getMonth() + 1).toString().padStart(2, '0')}`;
        dayColumn.appendChild(dayDateElement);

        // Add day column to timeline days
        timelineDays.appendChild(dayColumn);

        // Add tick to timeline line
        const tick = document.createElement('div');
        tick.className = 'timeline-tick';

        // Position the tick
        tick.style.left = `${dayCount * 65 + 32.5}px`; // Center in day column (adjusted for new width)

        // Add special classes for day start and week boundaries
        if (dayIndex === 0) {
          tick.classList.add('day-start');
        }
        if (dayIndex === 4) {
          tick.classList.add('week-boundary');
        }

        // Add current-day class if this is the current day
        if (isCurrentDay) {
          tick.classList.add('current-day');
        }

        timelineLine.appendChild(tick);

        dayCount++;
      }
    }

    // Assemble the timeline
    timelineContainer.appendChild(timelineWeeks);
    timelineContainer.appendChild(timelineDays);
    timelineContainer.appendChild(timelineLine);
    timelineContainer.appendChild(timelineEvents);

    // Add the timeline to the planning schedule
    planningSchedule.appendChild(timelineContainer);

    // Implement smooth, momentum-based scrolling
    let isDown = false;
    let startX;
    let scrollLeft;
    let velocity = 0;
    let lastX;
    let animationFrameId;
    let lastTimestamp = null;

    // Function to handle momentum scrolling
    function momentumScroll(timestamp) {
      if (!lastTimestamp) {
        lastTimestamp = timestamp;
        animationFrameId = requestAnimationFrame(momentumScroll);
        return;
      }

      const deltaTime = timestamp - lastTimestamp;
      lastTimestamp = timestamp;

      // Apply friction to slow down the scrolling
      velocity *= 0.95;

      // If velocity is very small, stop the animation
      if (Math.abs(velocity) < 0.5) {
        cancelAnimationFrame(animationFrameId);
        lastTimestamp = null;
        return;
      }

      // Apply velocity to scroll position
      planningSchedule.scrollLeft -= velocity * (deltaTime / 16); // Normalize to ~60fps

      // Continue the animation
      animationFrameId = requestAnimationFrame(momentumScroll);
    }

    planningSchedule.addEventListener('mousedown', (e) => {
      // Prevent default to avoid text selection during drag
      e.preventDefault();

      // Stop any ongoing momentum scrolling
      cancelAnimationFrame(animationFrameId);
      lastTimestamp = null;
      velocity = 0;

      isDown = true;
      planningSchedule.style.cursor = 'grabbing';
      startX = e.pageX - planningSchedule.getBoundingClientRect().left;
      lastX = e.pageX;
      scrollLeft = planningSchedule.scrollLeft;
    });

    // Handle mouse leave - stop dragging
    planningSchedule.addEventListener('mouseleave', () => {
      if (isDown) {
        isDown = false;
        planningSchedule.style.cursor = 'grab';

        // Start momentum scrolling with current velocity
        if (Math.abs(velocity) > 0.5) {
          lastTimestamp = null;
          animationFrameId = requestAnimationFrame(momentumScroll);
        }
      }
    });

    // Handle mouse up - stop dragging
    planningSchedule.addEventListener('mouseup', () => {
      if (isDown) {
        isDown = false;
        planningSchedule.style.cursor = 'grab';

        // Start momentum scrolling with current velocity
        if (Math.abs(velocity) > 0.5) {
          lastTimestamp = null;
          animationFrameId = requestAnimationFrame(momentumScroll);
        }
      }
    });

    // Handle mouse move - perform scrolling
    planningSchedule.addEventListener('mousemove', (e) => {
      if (!isDown) return;
      e.preventDefault();

      const x = e.pageX - planningSchedule.getBoundingClientRect().left;
      const walk = (x - startX) * 1.2; // Reduced multiplier for more precise control

      // Calculate velocity based on mouse movement
      const currentX = e.pageX;
      velocity = lastX - currentX;
      lastX = currentX;

      // Apply scrolling directly
      planningSchedule.scrollLeft = scrollLeft - walk;
    });

    // Function to scroll to current week with smooth animation
    function scrollToCurrentWeek() {
      const weekWidth = 325; // Week width (5 days × 65px)

      // Calculate the position to center the current week in the viewport
      const containerWidth = planningSchedule.offsetWidth;
      const targetScrollLeft = (currentWeek - 1) * weekWidth - (containerWidth - weekWidth) / 2;

      // Ensure we don't scroll to a negative position
      const scrollPosition = Math.max(0, targetScrollLeft);

      // Cancel any ongoing momentum scrolling
      cancelAnimationFrame(animationFrameId);

      // Use smooth scrolling behavior
      planningSchedule.style.scrollBehavior = 'smooth';
      planningSchedule.scrollLeft = scrollPosition;

      // Reset scroll behavior after animation completes
      setTimeout(() => {
        planningSchedule.style.scrollBehavior = 'auto';
      }, 500);
    }

    // Scroll to current week initially
    scrollToCurrentWeek();

    // Add event listener to the current week button
    currentWeekBtn.addEventListener('click', () => {
      // Scroll to current week in the planning schedule
      scrollToCurrentWeek();

      // Also scroll to today in the activities timeline
      const activitiesTimeline = document.getElementById('activities-timeline');
      if (activitiesTimeline) {
        // First, try to find today's activity point
        const todayActivityPoint = activitiesTimeline.querySelector('.activity-point.today-activity-point');

        if (todayActivityPoint) {
          // If we found today's activity point, scroll to it
          console.log('Scrolling to today\'s activity point from current week button');
          scrollToElement(activitiesTimeline, todayActivityPoint);
        } else {
          // If no today's activity point, try to find the today marker
          const todayMarker = activitiesTimeline.querySelector('.today-marker');
          if (todayMarker) {
            console.log('Scrolling to today marker from current week button');
            scrollToElement(activitiesTimeline, todayMarker);
          }
        }
      }
    });

    // Add event listener to the refresh button
    refreshBtn.addEventListener('click', () => {
      console.log('Refreshing training schedules, activities timeline and statistics...');



      // Clear existing event rows
      timelineEvents.innerHTML = '';

      // Fetch and display training schedules again
      fetchTrainingSchedules(timelineEvents, firstDayOfYear);



      // Show a brief animation on the refresh button
      refreshBtn.classList.add('refreshing');
      setTimeout(() => {
        refreshBtn.classList.remove('refreshing');
      }, 1000);

      // Refresh the statistics (KPIs)
      loadStatistics().then(() => {
        // After statistics are loaded, update the chart with the current period
        if (window.currentComparisonPeriod) {
          console.log('Refreshing with current period:', window.currentComparisonPeriod);
          // Find the active toggle button and trigger a click on it
          const activeToggleBtn = document.querySelector(`.comparison-toggle-btn[data-period="${window.currentComparisonPeriod}"]`);
          if (activeToggleBtn) {
            // Update the UI to reflect the current period
            document.querySelectorAll('.comparison-toggle-btn').forEach(btn => {
              btn.classList.remove('active');
            });
            activeToggleBtn.classList.add('active');

            // Move the slider
            const toggleSlider = document.querySelector('.toggle-slider');
            if (toggleSlider) {
              if (window.currentComparisonPeriod === 'month') {
                toggleSlider.classList.add('right');
              } else {
                toggleSlider.classList.remove('right');
              }
            }

            // Update the chart with the current period
            updateActivityChart(window.currentComparisonPeriod);
          }
        }
      });

      // Also refresh the activities timeline
      initActivitiesTimeline().then(() => {
        // After timeline is initialized, scroll to today's activity or marker
        setTimeout(() => {
          // First, try to find today's activity point
          const activitiesTimeline = document.getElementById('activities-timeline');
          if (activitiesTimeline) {
            const todayActivityPoint = activitiesTimeline.querySelector('.activity-point.today-activity-point');

            if (todayActivityPoint) {
              // If we found today's activity point, scroll to it
              console.log('Scrolling to today\'s activity point after refresh from planning');
              scrollToElement(activitiesTimeline, todayActivityPoint);
            } else {
              // If no today's activity point, try to find the today marker
              const todayMarker = activitiesTimeline.querySelector('.today-marker');
              if (todayMarker) {
                console.log('Scrolling to today marker after refresh from planning');
                scrollToElement(activitiesTimeline, todayMarker);
              }
            }
          }
        }, 200); // Wait a bit longer to ensure the timeline is fully rendered
      });
    });
  }

  // Initialize the planning schedule when the DOM is loaded
  initPlanningSchedule();

  // Initialize the activities timeline and ensure it scrolls to today
  initActivitiesTimeline().then(() => {
    // After timeline is initialized, scroll to today's activity or marker
    setTimeout(() => {
      const activitiesTimeline = document.getElementById('activities-timeline');
      if (activitiesTimeline) {
        // First, try to find today's activity point
        const todayActivityPoint = activitiesTimeline.querySelector('.activity-point.today-activity-point');

        if (todayActivityPoint) {
          // If we found today's activity point, scroll to it
          console.log('Scrolling to today\'s activity point on initial load');
          scrollToElement(activitiesTimeline, todayActivityPoint);
        } else {
          // If no today's activity point, try to find the today marker
          const todayMarker = activitiesTimeline.querySelector('.today-marker');
          if (todayMarker) {
            console.log('Scrolling to today marker on initial load');
            scrollToElement(activitiesTimeline, todayMarker);
          }
        }
      }
    }, 300); // Wait a bit longer for initial load
  });

  // Variable pour stocker le domaine actuellement sélectionné
  let currentDomain = 'Tout';

  // Fonction pour charger les domaines et initialiser les boutons de filtre
  async function loadDomains() {
    console.log('Loading domains...');
    const domainFilterElement = document.getElementById('domain-filter');

    if (!domainFilterElement) {
      console.error('Domain filter element not found');
      return;
    }

    // Afficher le message de chargement
    domainFilterElement.innerHTML = '<div class="loading-message">Chargement des domaines...</div>';

    try {
      // Récupérer les formations uniques depuis la base de données (sans filtre pour obtenir tous les domaines)
      const response = await window.api.getUniqueTrainings();

      if (!response.success) {
        console.error('Error fetching domains:', response.error);
        domainFilterElement.innerHTML = '<div class="error-message">Erreur lors du chargement des domaines</div>';
        return;
      }

      const domains = response.domains;
      const domainCounts = response.domainCounts;
      const totalCount = response.totalCount;

      console.log(`Retrieved ${domains.length} domains with counts`);

      // Vérifier si nous avons des domaines
      if (!domains || domains.length === 0) {
        domainFilterElement.innerHTML = '<div class="placeholder-message">Aucun domaine trouvé</div>';
        return;
      }

      // Vider la liste
      domainFilterElement.innerHTML = '';

      // Créer le conteneur pour le bouton "Tout"
      const allContainer = document.createElement('div');
      allContainer.className = 'domain-container';

      // Créer le bouton "Tout" en premier
      const allButton = document.createElement('button');
      allButton.className = 'domain-button active'; // Actif par défaut
      allButton.textContent = 'Tout';
      allButton.dataset.domain = 'Tout';

      // Créer le compteur pour "Tout" et l'ajouter à l'intérieur du bouton
      const allCount = document.createElement('div');
      allCount.className = 'domain-count';
      allCount.textContent = totalCount || 0;

      // Ajouter un gestionnaire d'événements pour le bouton "Tout"
      allButton.addEventListener('click', () => {
        // Mettre à jour le domaine actuel
        currentDomain = 'Tout';

        // Mettre à jour l'état actif des boutons
        document.querySelectorAll('.domain-button').forEach(btn => {
          btn.classList.remove('active');
        });
        allButton.classList.add('active');

        // Charger les formations pour ce domaine
        loadUniqueTrainings(currentDomain);
      });

      // Ajouter le compteur au bouton
      allButton.appendChild(allCount);

      // Ajouter le bouton au conteneur
      allContainer.appendChild(allButton);

      // Ajouter le conteneur au filtre
      domainFilterElement.appendChild(allContainer);

      // Ajouter chaque domaine comme bouton avec son compteur
      domains.forEach(domain => {
        // Créer le conteneur pour le bouton
        const domainContainer = document.createElement('div');
        domainContainer.className = 'domain-container';

        // Créer le bouton de domaine
        const domainButton = document.createElement('button');
        domainButton.className = 'domain-button';
        domainButton.textContent = domain;
        domainButton.dataset.domain = domain;

        // Créer le compteur pour ce domaine et l'ajouter à l'intérieur du bouton
        const domainCount = document.createElement('div');
        domainCount.className = 'domain-count';
        domainCount.textContent = domainCounts[domain] || 0;

        // Ajouter un gestionnaire d'événements pour le bouton de domaine
        domainButton.addEventListener('click', () => {
          // Mettre à jour le domaine actuel
          currentDomain = domain;

          // Mettre à jour l'état actif des boutons
          document.querySelectorAll('.domain-button').forEach(btn => {
            btn.classList.remove('active');
          });
          domainButton.classList.add('active');

          // Charger les formations pour ce domaine
          loadUniqueTrainings(currentDomain);
        });

        // Ajouter le compteur au bouton
        domainButton.appendChild(domainCount);

        // Ajouter le bouton au conteneur
        domainContainer.appendChild(domainButton);

        // Ajouter le conteneur au filtre
        domainFilterElement.appendChild(domainContainer);
      });

      // Charger les formations avec le domaine par défaut (Tout)
      loadUniqueTrainings(currentDomain);

      // Afficher le placeholder initial dans la vue de droite
      clearTrainingDetails();

    } catch (error) {
      console.error('Error loading domains:', error);
      domainFilterElement.innerHTML = '<div class="error-message">Erreur lors du chargement des domaines</div>';
    }
  }

  // La fonction calculateTrainingDuration a été supprimée car elle n'est plus utilisée

  // Fonction pour formater une date en français
  function formatDateFr(date) {
    // Convertir en objet Date si c'est une chaîne
    const dateObj = date instanceof Date ? date : new Date(date);

    // Options de formatage
    const options = { day: 'numeric', month: 'long', year: 'numeric' };

    // Formater la date
    return dateObj.toLocaleDateString('fr-FR', options);
  }

  // Fonction pour calculer la durée entre deux dates
  function calculateDuration(startDate, endDate) {
    if (!startDate || !endDate || !(startDate instanceof Date) || !(endDate instanceof Date) || isNaN(startDate) || isNaN(endDate)) {
      return null;
    }

    // Calculer la différence en millisecondes
    const diffTime = Math.abs(endDate - startDate);

    // Convertir en jours
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Calculer les mois, semaines et jours restants
    const months = Math.floor(diffDays / 30);
    const remainingDays = diffDays % 30;
    const weeks = Math.floor(remainingDays / 7);
    const days = remainingDays % 7;

    // Formater le résultat
    if (months === 0) {
      // Moins d'un mois, afficher en semaines
      if (weeks === 0) {
        return `${diffDays} jour${diffDays > 1 ? 's' : ''}`;
      } else {
        if (days === 0) {
          return `${weeks} semaine${weeks > 1 ? 's' : ''}`;
        } else {
          return `${weeks} sem. ${days} jr${days > 1 ? 's' : ''}`;
        }
      }
    } else {
      // Plus d'un mois, afficher en mois, semaines et jours
      let result = `${months} mois`;

      if (weeks > 0) {
        result += ` ${weeks} sem.`;
      }

      if (days > 0) {
        result += ` ${days} jr${days > 1 ? 's' : ''}`;
      }

      return result;
    }
  }

  // Fonction pour effacer les détails de formation et afficher le placeholder
  function clearTrainingDetails() {
    console.log('Clearing training details and showing placeholder');

    // Effacer l'en-tête de formation
    const trainingHeader = document.getElementById('training-header');
    if (trainingHeader) {
      trainingHeader.innerHTML = '';
    }

    // Afficher le placeholder dans la zone de détails
    const trainingDetails = document.getElementById('training-details');
    if (trainingDetails) {
      trainingDetails.innerHTML = `
        <div class="placeholder-message">
          <i class="fas fa-graduation-cap"></i>
          <h3>Veuillez sélectionner une formation</h3>
          <p>Cliquez sur une formation dans la liste pour voir ses détails</p>
        </div>
      `;
    }

    // Effacer la zone d'informations supplémentaires si elle existe
    const trainingAdditionalInfo = document.getElementById('training-additional-info');
    if (trainingAdditionalInfo) {
      trainingAdditionalInfo.innerHTML = `
        <div class="placeholder-message">
          <i class="fas fa-info-circle"></i>
          <p>Informations supplémentaires apparaîtront ici</p>
        </div>
      `;
    }
  }

  // Fonction pour charger les formations uniques
  async function loadUniqueTrainings(domain = 'Tout') {
    console.log(`Loading unique trainings for domain: ${domain}...`);
    const trainingsListElement = document.getElementById('trainings-list');

    if (!trainingsListElement) {
      console.error('Trainings list element not found');
      return;
    }

    // Afficher le message de chargement
    trainingsListElement.innerHTML = '<div class="loading-message">Chargement des formations...</div>';

    try {
      // Récupérer les formations uniques depuis la base de données
      const response = await window.api.getUniqueTrainings(domain);

      if (!response.success) {
        console.error('Error fetching unique trainings:', response.error);
        trainingsListElement.innerHTML = '<div class="error-message">Erreur lors du chargement des formations</div>';
        return;
      }

      const trainings = response.data;
      console.log(`Retrieved ${trainings.length} unique trainings for domain: ${domain}`);

      // Vérifier si nous avons des formations
      if (!trainings || trainings.length === 0) {
        trainingsListElement.innerHTML = '<div class="placeholder-message">Aucune formation trouvée</div>';
        return;
      }

      // Vider la liste
      trainingsListElement.innerHTML = '';

      // Ajouter chaque formation à la liste
      trainings.forEach(training => {
        const trainingItem = document.createElement('div');
        trainingItem.className = 'training-item';
        trainingItem.dataset.name = training.nom_formation;

        const nameElement = document.createElement('div');
        nameElement.className = 'training-item-name';
        nameElement.textContent = training.nom_formation;

        // Ajouter le domaine uniquement si on affiche tous les domaines
        if (domain === 'Tout') {
          const domainElement = document.createElement('div');
          domainElement.className = 'training-item-domain';
          domainElement.textContent = training.domaine || 'Domaine non spécifié';
          trainingItem.appendChild(domainElement);
        }

        // Ajouter le type si disponible
        if (training.type) {
          const typeElement = document.createElement('div');
          typeElement.className = 'training-item-type';
          typeElement.textContent = training.type;
          trainingItem.appendChild(typeElement);
        }

        const countElement = document.createElement('div');
        countElement.className = 'training-item-count';

        // Formater la date de fin la plus récente
        let lastEndDateStr = '';
        if (training.last_end_date) {
          const lastEndDate = new Date(training.last_end_date);
          lastEndDateStr = formatDateFr(lastEndDate);
        }

        // Afficher le nombre d'instances et la date de fin la plus récente
        if (lastEndDateStr) {
          countElement.innerHTML = `${training.instance_count || 0} instance${training.instance_count > 1 ? 's' : ''} <span class="bullet-separator">•</span> <span class="end-date">${lastEndDateStr}</span>`;
        } else {
          countElement.textContent = `${training.instance_count || 0} instance${training.instance_count > 1 ? 's' : ''}`;
        }

        trainingItem.appendChild(nameElement);
        trainingItem.appendChild(countElement);

        // Ajouter un gestionnaire d'événements pour afficher les détails de la formation
        trainingItem.addEventListener('click', async () => {
          // Vérifier si l'élément est déjà actif
          const isCurrentlyActive = trainingItem.classList.contains('active');

          // Supprimer la classe active de tous les éléments
          document.querySelectorAll('.training-item').forEach(item => {
            item.classList.remove('active');
          });

          // Si l'élément était déjà actif, le désélectionner et afficher le placeholder
          if (isCurrentlyActive) {
            clearTrainingDetails();
            return;
          }

          // Ajouter la classe active à l'élément cliqué
          trainingItem.classList.add('active');

          // Mettre à jour l'en-tête avec le nom de la formation et le badge de stagiaires
          const trainingHeader = document.getElementById('training-header');
          trainingHeader.innerHTML = `
            <span class="training-name-text">${training.nom_formation}</span>
            <button class="total-trainees-badge clickable-badge" id="total-trainees-badge" title="Cliquez pour voir tous les stagiaires">
              <span>👥</span>
              <span id="total-trainees-count">Chargement...</span>
              <span>stagiaires</span>
              <span class="total-radied-badge" id="total-radied-badge">
                (<span id="total-radied-count">0</span> radiés)
              </span>
            </button>
            <span class="floating-tranche-badge" id="floating-tranche-badge">
              <span>📋</span>
              <span id="floating-tranche-text">Tranche</span>
            </span>
          `;

          // Ajouter l'événement de clic sur le badge des stagiaires
          const totalTraineesBadge = document.getElementById('total-trainees-badge');
          if (totalTraineesBadge) {
            console.log('Adding click event to total trainees badge');
            totalTraineesBadge.addEventListener('click', (e) => {
              console.log('Total trainees badge clicked!', training.nom_formation);
              e.preventDefault();
              e.stopPropagation();
              try {
                window.showAllTraineesModal(training.nom_formation);
              } catch (error) {
                console.error('Error opening all trainees modal:', error);
              }
            });
          } else {
            console.error('Total trainees badge not found!');
          }

          // Charger le nombre total de stagiaires pour cette formation
          loadTotalTraineesCount(training.nom_formation);

          // Charger le nombre total de stagiaires radiés pour cette formation
          loadTotalRadiedTraineesCount(training.nom_formation);

          // Masquer le badge flottant par défaut
          hideFloatingBadge();

          // Afficher un message de chargement
          document.getElementById('training-details').innerHTML = `
            <div class="loading-message">Chargement des détails...</div>
          `;

          // Nous n'avons plus besoin d'afficher les informations complémentaires
          // car le panneau inférieur a été supprimé

          try {
            // Récupérer les détails de la formation
            const response = await window.api.getTrainingDetails(training.nom_formation);

            if (!response.success) {
              console.error('Error fetching training details:', response.error);
              document.getElementById('training-details').innerHTML = `
                <div class="error-message">Erreur lors du chargement des détails: ${response.error}</div>
              `;
              return;
            }

            const details = response.data;
            console.log('Training details:', details);

            // Créer l'affichage des détails avec la timeline
            const startDate = details.du ? new Date(details.du) : null;
            const endDate = details.au ? new Date(details.au) : null;
            const extendedDate = details.extended_to ? new Date(details.extended_to) : null;

            if (!startDate || !endDate) {
              document.getElementById('training-details').innerHTML = `
                <div class="error-message">Dates de formation manquantes</div>
              `;
              return;
            }

            // Afficher les détails de la formation

            // Nous n'avons plus besoin de formater les dates car nous utilisons des KPIs à la place

            // Créer le HTML pour les détails
            let detailsHTML = `
              <div class="training-kpis-container">
                <h3 class="kpis-title">Indicateurs liés à la formation</h3>
                <div class="training-kpis">
                  <div class="kpi-item" id="instances-count-kpi">
                    <div class="kpi-value">...</div>
                    <div class="kpi-label">Instances</div>
                  </div>
                  <div class="kpi-item" id="types-kpi">
                    <div class="kpi-value">...</div>
                    <div class="kpi-label">Types</div>
                  </div>
                  <div class="kpi-item" id="extended-kpi">
                    <div class="kpi-value">...</div>
                    <div class="kpi-label">Prolongées</div>
                  </div>
                  <div class="kpi-item" id="rescheduled-kpi">
                    <div class="kpi-value">...</div>
                    <div class="kpi-label">Reprogrammées</div>
                  </div>
                </div>
              </div>
            `;

            // Ajouter un conteneur pour la liste des instances
            detailsHTML += `
              <div class="instances-list-container">
                <div class="instances-list-title">Tranches</div>
                <div class="instances-list" id="instances-list">
                  <div class="loading-message">Chargement des instances...</div>
                </div>
              </div>
              <div class="instance-details-container" id="instance-details-container"></div>
            `;

            // Mettre à jour l'affichage des détails
            document.getElementById('training-details').innerHTML = detailsHTML;

            // Charger les instances de la formation
            loadTrainingInstances(training.nom_formation);

            // Nous n'avons plus besoin d'afficher les informations complémentaires
            // car le panneau inférieur a été supprimé

          } catch (error) {
            console.error('Error displaying training details:', error);
            document.getElementById('training-details').innerHTML = `
              <div class="error-message">Erreur lors de l'affichage des détails: ${error.message}</div>
            `;
          }
        });

        trainingsListElement.appendChild(trainingItem);
      });

    } catch (error) {
      console.error('Error loading unique trainings:', error);
      trainingsListElement.innerHTML = '<div class="error-message">Erreur lors du chargement des formations</div>';
    }
  }

  // Fonction pour charger les instances d'une formation
  async function loadTrainingInstances(nomFormation) {
    console.log(`Loading instances for training: ${nomFormation}...`);
    const instancesListElement = document.getElementById('instances-list');

    if (!instancesListElement) {
      console.error('Instances list element not found');
      return;
    }

    try {
      // Récupérer les instances depuis la base de données
      const response = await window.api.getTrainingInstances(nomFormation);

      if (!response.success) {
        console.error('Error fetching training instances:', response.error);
        instancesListElement.innerHTML = `<div class="error-message">Erreur: ${response.error}</div>`;
        return;
      }

      const instances = response.data;
      console.log(`Retrieved ${instances.length} instances for training: ${nomFormation}`);

      // Vérifier si nous avons des instances
      if (!instances || instances.length === 0) {
        instancesListElement.innerHTML = '<div class="placeholder-message">Aucune instance trouvée</div>';

        // Mettre à jour les KPIs avec des valeurs par défaut
        updateTrainingKPIs(0, {}, 0, 0);
        return;
      }

      // Calculer les KPIs
      const totalInstances = instances.length;

      // Compter les types de formation
      const typesCounts = {};
      instances.forEach(instance => {
        const type = instance.type || 'Non spécifié';
        typesCounts[type] = (typesCounts[type] || 0) + 1;
      });

      // Compter les instances prolongées et reprogrammées
      let extendedCount = 0;
      let rescheduledCount = 0;

      instances.forEach(instance => {
        if (instance.extended_to) {
          extendedCount++;
        }
        if (instance.rescheduled_du || instance.rescheduled_au) {
          rescheduledCount++;
        }
      });

      // Mettre à jour les KPIs
      updateTrainingKPIs(totalInstances, typesCounts, extendedCount, rescheduledCount);

      // Vider la liste
      instancesListElement.innerHTML = '';

      // Ajouter chaque instance à la liste
      instances.forEach((instance, index) => {
        const instanceItem = document.createElement('div');
        instanceItem.className = 'instance-item';
        instanceItem.dataset.id = instance.id;

        // Déterminer les dates à afficher
        let startDate = instance.du ? new Date(instance.du) : null;
        let endDate = instance.au ? new Date(instance.au) : null;

        // Utiliser rescheduled_du si disponible
        if (instance.rescheduled_du) {
          startDate = new Date(instance.rescheduled_du);
        }

        // Utiliser extended_to ou rescheduled_au si disponible
        if (instance.rescheduled_au) {
          endDate = new Date(instance.rescheduled_au);
        } else if (instance.extended_to) {
          endDate = new Date(instance.extended_to);
        }

        // Formater les dates
        const startDateStr = startDate ? formatDateFr(startDate) : 'Date non spécifiée';
        const endDateStr = endDate ? formatDateFr(endDate) : 'Date non spécifiée';

        // Vérifier si l'instance a été prolongée ou reprogrammée
        const isExtended = instance.extended_to ? true : false;
        const isRescheduled = instance.rescheduled_du || instance.rescheduled_au ? true : false;

        // Vérifier si l'instance est en cours et calculer le pourcentage d'avancement
        const today = new Date();
        const isOngoing = startDate && endDate &&
                         today >= startDate &&
                         today <= endDate;

        // Calculer le pourcentage d'avancement si l'instance est en cours
        let progressPercentage = 0;
        if (isOngoing && startDate && endDate) {
          const totalDuration = endDate.getTime() - startDate.getTime();
          const elapsedDuration = today.getTime() - startDate.getTime();
          progressPercentage = Math.round((elapsedDuration / totalDuration) * 100);
          // S'assurer que le pourcentage est entre 0 et 100
          progressPercentage = Math.max(0, Math.min(100, progressPercentage));
        }

        // Calculer la durée de l'instance
        let durationStr = '';
        if (startDate && endDate) {
          durationStr = calculateDuration(startDate, endDate);
        }

        // Préparer l'affichage de la date de fin
        let endDateDisplay = '';
        if (isExtended && instance.au) {
          // Si l'instance a été prolongée, afficher la date initiale barrée et la nouvelle date
          const initialEndDate = new Date(instance.au);
          const initialEndDateStr = formatDateFr(initialEndDate);
          endDateDisplay = `
            <div>Au: <span class="date-strikethrough">${initialEndDateStr}</span></div>
            <div class="extended-date">Prolongée au: ${endDateStr}</div>
          `;
        } else {
          // Sinon, afficher la date de fin normalement
          endDateDisplay = `<div>Au: ${endDateStr}</div>`;
        }

        // Ajouter une classe spéciale si l'instance est en cours
        if (isOngoing) {
          instanceItem.classList.add('ongoing-instance');
          // Ajouter l'attribut data-progress pour le pourcentage
          instanceItem.dataset.progress = progressPercentage;
        }

        // Créer le contenu de l'élément
        instanceItem.innerHTML = `
          <div class="instance-title">
            ${instance.tranche || `Tranche ${index + 1}`}
            ${durationStr ? `<span class="bullet-separator">•</span><span class="duration-tag">${durationStr}</span>` : ''}
          </div>
          <div class="instance-dates">
            <div>Du: ${startDateStr}</div>
            ${endDateDisplay}
            ${isRescheduled ? '<div class="instance-status rescheduled">Reprogrammée</div>' : ''}
          </div>
          ${isOngoing ? `
            <div class="progress-border" style="--progress: ${progressPercentage}%"></div>
          ` : ''}
        `;

        // Ajouter un gestionnaire d'événements pour afficher les détails de l'instance
        instanceItem.addEventListener('click', () => {
          // Supprimer la classe active de tous les éléments
          document.querySelectorAll('.instance-item').forEach(item => {
            item.classList.remove('active');
          });

          // Ajouter la classe active à l'élément cliqué
          instanceItem.classList.add('active');

          // Afficher les détails de l'instance
          displayInstanceDetails(instance);
        });

        instancesListElement.appendChild(instanceItem);
      });

      // Sélectionner la première instance par défaut
      if (instances.length > 0) {
        const firstInstanceItem = instancesListElement.querySelector('.instance-item');
        if (firstInstanceItem) {
          firstInstanceItem.classList.add('active');
          displayInstanceDetails(instances[0]);
        }
      }

    } catch (error) {
      console.error('Error loading training instances:', error);
      instancesListElement.innerHTML = `<div class="error-message">Erreur: ${error.message}</div>`;

      // Mettre à jour les KPIs avec des valeurs par défaut en cas d'erreur
      updateTrainingKPIs(0, {}, 0, 0);
    }
  }

  // Fonction pour mettre à jour les KPIs de formation
  function updateTrainingKPIs(totalInstances, typesCounts, extendedCount, rescheduledCount) {
    // Mettre à jour le nombre total d'instances
    const instancesCountElement = document.getElementById('instances-count-kpi');
    if (instancesCountElement) {
      const valueElement = instancesCountElement.querySelector('.kpi-value');
      if (valueElement) {
        valueElement.textContent = totalInstances.toString().padStart(2, '0');
      }
    }

    // Mettre à jour les types
    const typesElement = document.getElementById('types-kpi');
    if (typesElement) {
      const valueElement = typesElement.querySelector('.kpi-value');
      if (valueElement) {
        const typesKeys = Object.keys(typesCounts);

        if (typesKeys.length === 0) {
          valueElement.textContent = '-';
        } else if (typesKeys.length === 1) {
          // Si un seul type, afficher le nom du type
          valueElement.textContent = typesKeys[0];
        } else {
          // Si plusieurs types, afficher le nombre par type
          let typesText = '';
          typesKeys.forEach(type => {
            if (typesText) typesText += ' | ';
            typesText += `${typesCounts[type].toString().padStart(2, '0')} ${type}`;
          });
          valueElement.textContent = typesText;
        }
      }
    }

    // Mettre à jour le nombre d'instances prolongées
    const extendedElement = document.getElementById('extended-kpi');
    if (extendedElement) {
      const valueElement = extendedElement.querySelector('.kpi-value');
      if (valueElement) {
        valueElement.textContent = extendedCount.toString().padStart(2, '0');
      }
    }

    // Mettre à jour le nombre d'instances reprogrammées
    const rescheduledElement = document.getElementById('rescheduled-kpi');
    if (rescheduledElement) {
      const valueElement = rescheduledElement.querySelector('.kpi-value');
      if (valueElement) {
        valueElement.textContent = rescheduledCount.toString().padStart(2, '0');
      }
    }
  }

  // Fonction pour afficher les détails d'une instance
  function displayInstanceDetails(instance) {
    console.log('Displaying details for instance:', instance);
    const detailsContainer = document.getElementById('instance-details-container');

    if (!detailsContainer) {
      console.error('Instance details container not found');
      return;
    }

    // Déterminer les dates à afficher
    let startDate = instance.du ? new Date(instance.du) : null;
    let endDate = instance.au ? new Date(instance.au) : null;
    let hasRescheduled = false;
    let hasExtended = false;

    // Utiliser rescheduled_du si disponible
    if (instance.rescheduled_du) {
      startDate = new Date(instance.rescheduled_du);
      hasRescheduled = true;
    }

    // Utiliser extended_to ou rescheduled_au si disponible
    if (instance.rescheduled_au) {
      endDate = new Date(instance.rescheduled_au);
      hasRescheduled = true;
    } else if (instance.extended_to) {
      endDate = new Date(instance.extended_to);
      hasExtended = true;
    }

    // Formater les dates
    const startDateStr = startDate ? formatDateFr(startDate) : 'Date non spécifiée';
    const endDateStr = endDate ? formatDateFr(endDate) : 'Date non spécifiée';

    // Vérifier si l'instance est en cours et calculer le pourcentage d'avancement
    const today = new Date();
    const isOngoing = startDate && endDate &&
                     today >= startDate &&
                     today <= endDate;

    // Calculer le pourcentage d'avancement si l'instance est en cours
    let progressPercentage = 0;
    if (isOngoing && startDate && endDate) {
      const totalDuration = endDate.getTime() - startDate.getTime();
      const elapsedDuration = today.getTime() - startDate.getTime();
      progressPercentage = Math.round((elapsedDuration / totalDuration) * 100);
      // S'assurer que le pourcentage est entre 0 et 100
      progressPercentage = Math.max(0, Math.min(100, progressPercentage));
    }

    // Calculer la durée de l'instance
    let durationStr = '';
    if (startDate && endDate) {
      durationStr = calculateDuration(startDate, endDate);
    }

    // Préparer l'affichage de la date de fin pour les détails
    let endDateHTML = '';
    if (hasExtended && instance.au) {
      // Si l'instance a été prolongée, afficher la date initiale barrée et la nouvelle date
      const initialEndDate = new Date(instance.au);
      const initialEndDateStr = formatDateFr(initialEndDate);
      endDateHTML = `<div><strong>Date de fin initiale:</strong> <span class="date-strikethrough">${initialEndDateStr}</span></div>
                     <div><strong>Date de fin prolongée:</strong> ${endDateStr}</div>`;
    } else {
      // Sinon, afficher la date de fin normalement
      endDateHTML = `<div><strong>Date de fin:</strong> ${endDateStr}${hasRescheduled ? ' (reprogrammée)' : ''}</div>`;
    }

    // Créer le HTML pour les boutons de bascule
    let toggleButtonsHTML = `
      <div class="instance-toggle-buttons">
        <div class="toggle-buttons-group">
          <button class="instance-toggle-btn active" data-tab="details">Détails</button>
          <button class="instance-toggle-btn" data-tab="trainees">Stagiaires</button>
          <button class="instance-toggle-btn" data-tab="other">Autre</button>
        </div>
      </div>
    `;

    // Créer le HTML pour l'onglet des détails
    let detailsTabHTML = `
      <div class="instance-tab-content active" id="details-tab">
        <div class="instance-info">
          <div><strong>Formation:</strong> ${instance.nom_formation}</div>
          <div><strong>Date de début:</strong> ${startDateStr}${hasRescheduled ? ' (reprogrammée)' : ''}</div>
          ${endDateHTML}
          ${durationStr ? `<div><strong>Durée:</strong> ${durationStr}</div>` : ''}
          ${isOngoing ? `<div><strong>Statut:</strong> <span style="color: #4CAF50;">En cours (${progressPercentage}% terminé)</span></div>` : ''}
    `;

    // Ajouter des informations supplémentaires si disponibles
    if (instance.domaine) {
      detailsTabHTML += `<div><strong>Domaine:</strong> ${instance.domaine}</div>`;
    }

    if (instance.type) {
      detailsTabHTML += `<div><strong>Type:</strong> ${instance.type}</div>`;
    }

    // Ajouter l'ID de l'instance
    if (instance.id) {
      detailsTabHTML += `<div><strong>ID Instance:</strong> ${instance.id}</div>`;
    }

    // Ajouter le nombre de stagiaires (sera mis à jour après le chargement)
    detailsTabHTML += `<div><strong>Nombre de stagiaires:</strong> <span id="trainees-count">Chargement...</span></div>`;

    // Ajouter des informations sur les modifications si disponibles
    if (hasExtended) {
      detailsTabHTML += `<div><strong>Statut de modification:</strong> <span style="color: #FF9800;">Formation prolongée</span></div>`;
    }
    if (hasRescheduled) {
      detailsTabHTML += `<div><strong>Statut de modification:</strong> <span style="color: #2196F3;">Formation reprogrammée</span></div>`;
    }

    detailsTabHTML += `</div>
      </div>
    `;

    // Créer le HTML pour l'onglet des stagiaires
    let traineesTabHTML = `
      <div class="instance-tab-content" id="trainees-tab">
        <div class="trainees-table-container">
          <div class="loading-message">Chargement des stagiaires...</div>
        </div>
      </div>
    `;

    // Créer le HTML pour l'onglet "Autre"
    let otherTabHTML = `
      <div class="instance-tab-content" id="other-tab">
        <div class="placeholder-message">Contenu à venir</div>
      </div>
    `;

    // Assembler le HTML complet (sans titre)
    let fullHTML = `
      ${toggleButtonsHTML}
      ${detailsTabHTML}
      ${traineesTabHTML}
      ${otherTabHTML}
    `;

    // Mettre à jour l'affichage des détails
    detailsContainer.innerHTML = fullHTML;

    // Ajouter les gestionnaires d'événements pour les boutons de bascule
    const toggleButtons = detailsContainer.querySelectorAll('.instance-toggle-btn');
    toggleButtons.forEach(button => {
      button.addEventListener('click', () => {
        // Retirer la classe active de tous les boutons
        toggleButtons.forEach(btn => btn.classList.remove('active'));
        // Ajouter la classe active au bouton cliqué
        button.classList.add('active');

        // Masquer tous les contenus d'onglet
        const tabContents = detailsContainer.querySelectorAll('.instance-tab-content');
        tabContents.forEach(content => content.classList.remove('active'));

        // Afficher le contenu de l'onglet correspondant
        const tabId = button.getAttribute('data-tab');
        const tabContent = detailsContainer.querySelector(`#${tabId}-tab`);
        if (tabContent) {
          tabContent.classList.add('active');

          // Gérer l'affichage du badge flottant et du filtre
          if (tabId === 'trainees') {
            // Afficher le badge quand l'onglet Stagiaires est sélectionné
            showFloatingBadge();
            // Afficher le filtre par fonction
            toggleFonctionFilter(true);
            // Charger les données des stagiaires
            if (instance.id) {
              loadTraineesForTraining(instance.id);
            }
          } else {
            // Masquer le badge pour tous les autres onglets
            hideFloatingBadge();
            // Masquer le filtre par fonction
            toggleFonctionFilter(false);
          }
        }
      });
    });

    // Charger le nombre de stagiaires immédiatement pour l'afficher dans l'onglet détails
    if (instance.id) {
      loadTraineesCount(instance.id);
    }

    // Sauvegarder l'instance sélectionnée et masquer le badge par défaut
    currentSelectedInstance = instance;
    hideFloatingBadge();
  }

  // Fonction pour afficher le badge flottant (nouvelle logique)
  function showFloatingBadge() {
    console.log('=== SHOWING FLOATING BADGE ===');

    if (!currentSelectedInstance) {
      console.log('No selected instance, cannot show badge');
      return;
    }

    // Vérifier si le badge existe déjà
    let badge = document.getElementById('floating-tranche-badge');
    if (!badge) {
      console.log('Badge not found in DOM');
      return;
    }

    // Mettre à jour le texte du badge
    const floatingText = document.getElementById('floating-tranche-text');
    if (floatingText) {
      const trancheName = currentSelectedInstance.tranche || `Tranche ${currentSelectedInstance.id}`;
      floatingText.textContent = `Sélectionnée : ${trancheName}`;
      console.log('Badge text updated to:', `Sélectionnée : ${trancheName}`);
    }

    // Afficher le badge avec animation
    badge.style.display = 'flex';
    setTimeout(() => {
      badge.classList.add('visible');
    }, 10);
  }

  // Fonction pour masquer le badge flottant (nouvelle logique)
  function hideFloatingBadge() {
    console.log('=== HIDING FLOATING BADGE ===');

    const badge = document.getElementById('floating-tranche-badge');
    if (badge) {
      badge.classList.remove('visible');
      setTimeout(() => {
        badge.style.display = 'none';
      }, 300); // Attendre la fin de l'animation
      console.log('Badge hidden');
    }
  }

  // Fonction pour charger uniquement le nombre de stagiaires
  async function loadTraineesCount(trainingId) {
    try {
      const response = await window.api.getTraineesForTraining(trainingId);
      const traineesCountElement = document.getElementById('trainees-count');

      if (traineesCountElement) {
        if (response.success) {
          const count = response.data.length;
          traineesCountElement.textContent = count;
        } else {
          traineesCountElement.textContent = 'Erreur';
        }
      }
    } catch (error) {
      console.error('Error loading trainees count:', error);
      const traineesCountElement = document.getElementById('trainees-count');
      if (traineesCountElement) {
        traineesCountElement.textContent = 'Erreur';
      }
    }
  }

  // Fonction pour charger le nombre total de stagiaires pour toutes les instances d'une formation
  async function loadTotalTraineesCount(trainingName) {
    console.log(`Loading total trainees count for training: ${trainingName}`);

    try {
      const response = await window.api.getTotalTraineesForTrainingName(trainingName);
      const totalTraineesCountElement = document.getElementById('total-trainees-count');

      console.log('Response from getTotalTraineesForTrainingName:', response);

      if (totalTraineesCountElement) {
        if (response && response.success) {
          const count = response.data;
          console.log(`Total trainees count: ${count}`);
          totalTraineesCountElement.textContent = count;
        } else {
          console.error('Failed to get total trainees count:', response?.error || 'Unknown error');
          totalTraineesCountElement.textContent = '0';
        }
      } else {
        console.error('total-trainees-count element not found');
      }
    } catch (error) {
      console.error('Error loading total trainees count:', error);
      const totalTraineesCountElement = document.getElementById('total-trainees-count');
      if (totalTraineesCountElement) {
        totalTraineesCountElement.textContent = '0';
      }
    }
  }

  // Fonction pour charger le nombre total de stagiaires radiés pour toutes les instances d'une formation
  async function loadTotalRadiedTraineesCount(trainingName) {
    console.log(`Loading total radied trainees count for training: ${trainingName}`);

    try {
      const response = await window.api.getTotalRadiedTraineesForTrainingName(trainingName);
      const totalRadiedCountElement = document.getElementById('total-radied-count');

      console.log('Response from getTotalRadiedTraineesForTrainingName:', response);

      if (totalRadiedCountElement) {
        if (response && response.success) {
          const count = response.data;
          console.log(`Total radied trainees count: ${count}`);
          totalRadiedCountElement.textContent = count;
        } else {
          console.error('Failed to get total radied trainees count:', response?.error || 'Unknown error');
          totalRadiedCountElement.textContent = '0';
        }
      } else {
        console.error('total-radied-count element not found');
      }
    } catch (error) {
      console.error('Error loading total radied trainees count:', error);
      const totalRadiedCountElement = document.getElementById('total-radied-count');
      if (totalRadiedCountElement) {
        totalRadiedCountElement.textContent = '0';
      }
    }
  }

  // Fonction pour charger les stagiaires d'une instance de formation
  async function loadTraineesForTraining(trainingId) {
    const traineesContainer = document.querySelector('#trainees-tab .trainees-table-container');

    if (!traineesContainer) {
      console.error('Trainees container not found');
      return;
    }

    // Afficher un message de chargement
    traineesContainer.innerHTML = '<div class="loading-message">Chargement des stagiaires...</div>';

    try {
      // Récupérer les stagiaires depuis la base de données
      const response = await window.api.getTraineesForTraining(trainingId);

      if (!response.success) {
        console.error('Failed to fetch trainees:', response.error);
        traineesContainer.innerHTML = `<div class="error-message">Erreur: ${response.error}</div>`;
        return;
      }

      const trainees = response.data;
      console.log(`Received ${trainees.length} trainees for training ID ${trainingId}`);

      // Récupérer les stagiaires radiés pour cette formation
      const radiedResponse = await window.api.getRadiedTrainees(trainingId);
      let radiedTrainees = [];

      if (radiedResponse.success) {
        radiedTrainees = radiedResponse.data;
        console.log(`Received ${radiedTrainees.length} radied trainees for training ID ${trainingId}`);
      } else {
        console.warn('Failed to fetch radied trainees:', radiedResponse.error);
      }

      // Créer un map des stagiaires radiés pour un accès rapide
      const radiedMap = {};
      radiedTrainees.forEach(radied => {
        radiedMap[radied.id_stagiaires] = {
          reference: radied.reference,
          motif: radied.motif
        };
      });

      // Marquer les stagiaires radiés et compter
      let radiedCount = 0;
      trainees.forEach(trainee => {
        if (radiedMap[trainee.id]) {
          trainee.isRadied = true;
          trainee.radiedInfo = radiedMap[trainee.id];
          radiedCount++;
        }
      });

      // Log détaillé des données pour débogage
      if (trainees.length > 0) {
        console.log('Sample trainee data:', trainees[0]);
        console.log('All fonction values:', trainees.map(t => t.fonction));
      }

      // Si aucun stagiaire n'est trouvé, afficher un message
      if (trainees.length === 0) {
        updateTraineesCountBadge(0, 0);
        currentTraineesFonctions = [];
        traineesContainer.innerHTML = '<div class="no-trainees-message">Aucun stagiaire inscrit pour cette formation</div>';
        return;
      }

      // Vérifier s'il y a au moins une fonction non-null
      const hasFonction = trainees.some(trainee => trainee.fonction && trainee.fonction.trim() !== '');
      console.log('Has fonction:', hasFonction);

      // Obtenir la liste unique des fonctions pour le filtre
      const fonctions = hasFonction ? [...new Set(trainees.map(t => t.fonction).filter(f => f && f.trim() !== ''))].sort() : [];

      // Mettre à jour le badge de comptage (toujours visible)
      updateTraineesCountBadge(trainees.length, radiedCount);

      // Sauvegarder les fonctions pour le filtre (sera affiché seulement si l'onglet est actif)
      currentTraineesFonctions = fonctions;

      // Grouper les stagiaires par fonction si applicable
      let groupedTrainees;
      if (hasFonction) {
        groupedTrainees = {};
        trainees.forEach(trainee => {
          const fonction = trainee.fonction && trainee.fonction.trim() !== '' ? trainee.fonction : 'Sans fonction';
          if (!groupedTrainees[fonction]) {
            groupedTrainees[fonction] = [];
          }
          groupedTrainees[fonction].push(trainee);
        });
      }

      // Sauvegarder les données pour le filtrage
      currentTraineesData = { trainees, hasFonction, groupedTrainees };

      // Créer la table des stagiaires
      renderTraineesTable(trainees, hasFonction, groupedTrainees);

    } catch (error) {
      console.error('Error loading trainees:', error);
      traineesContainer.innerHTML = `<div class="error-message">Erreur: ${error.message}</div>`;
    }
  }

  // Fonction pour mettre à jour seulement le badge de comptage (toujours visible)
  function updateTraineesCountBadge(totalCount, radiedCount = 0) {
    const traineesButton = document.querySelector('[data-tab="trainees"]');
    if (!traineesButton) return;

    // Créer le badge de comptage avec le nombre de radiés si > 0
    let badgeContent = `<span class="trainees-count-badge">${totalCount}</span>`;
    if (radiedCount > 0) {
      badgeContent += `<span class="trainees-radied-badge">(-${radiedCount})</span>`;
    }

    // Mettre à jour le texte du bouton avec le badge
    traineesButton.innerHTML = `Stagiaires ${badgeContent}`;
  }

  // Fonction pour afficher/masquer le filtre par fonction
  function toggleFonctionFilter(show) {
    // Supprimer le filtre existant
    const existingFilter = document.getElementById('fonction-filter');
    if (existingFilter) {
      existingFilter.remove();
    }

    if (show && currentTraineesFonctions.length > 0) {
      // Créer le filtre à l'extrême droite
      const filterHTML = `
        <select class="fonction-filter" id="fonction-filter">
          <option value="">Toutes les fonctions</option>
          ${currentTraineesFonctions.map(fonction => `<option value="${fonction}">${fonction}</option>`).join('')}
        </select>
      `;

      // Ajouter le filtre à l'extrême droite dans le même conteneur que les boutons
      const toggleButtonsContainer = document.querySelector('.instance-toggle-buttons');
      if (toggleButtonsContainer) {
        toggleButtonsContainer.insertAdjacentHTML('beforeend', filterHTML);

        // Ajouter l'événement de filtrage
        const filterSelect = document.getElementById('fonction-filter');
        if (filterSelect) {
          filterSelect.addEventListener('change', (e) => {
            filterTraineesByFonction(e.target.value);
          });
        }
      }
    }
  }

  // Fonction pour rendre la table des stagiaires
  function renderTraineesTable(trainees, hasFonction, groupedTrainees = null, filterFonction = '') {
    const traineesContainer = document.querySelector('#trainees-tab .trainees-table-container');
    if (!traineesContainer) return;

    // Filtrer les stagiaires si un filtre est appliqué
    let filteredTrainees = trainees;
    if (filterFonction) {
      filteredTrainees = trainees.filter(trainee =>
        trainee.fonction && trainee.fonction.trim() === filterFonction
      );
    }

    // Mettre à jour le compteur dans le badge
    const countBadge = document.querySelector('.trainees-count-badge');
    if (countBadge) {
      countBadge.textContent = filteredTrainees.length;
    }

    // Si aucun stagiaire après filtrage
    if (filteredTrainees.length === 0) {
      traineesContainer.innerHTML = '<div class="no-trainees-message">Aucun stagiaire trouvé pour ce filtre</div>';
      return;
    }

    // Créer l'en-tête de la table
    let tableHTML = `
      <table class="trainees-table">
        <thead>
          <tr>
            ${hasFonction ? '<th>Fonction</th>' : ''}
            <th>Grade</th>
            <th>Nom</th>
            <th>Prénom</th>
            <th>Mle</th>
            <th>Unité</th>
          </tr>
        </thead>
        <tbody>
    `;

    // Si on groupe par fonction et qu'aucun filtre n'est appliqué
    if (hasFonction && !filterFonction && groupedTrainees) {
      // Trier les fonctions (mettre "Sans fonction" à la fin)
      const sortedFonctions = Object.keys(groupedTrainees).sort((a, b) => {
        if (a === 'Sans fonction') return 1;
        if (b === 'Sans fonction') return -1;
        return a.localeCompare(b);
      });

      sortedFonctions.forEach((fonction, fonctionIndex) => {
        groupedTrainees[fonction].forEach((trainee, index) => {
          // Déterminer les classes CSS pour la séparation visuelle
          let rowClasses = 'trainee-row'; // Toujours commencer par la classe de base
          if (index === 0) {
            // Première ligne du groupe
            if (fonctionIndex === 0) {
              // Premier groupe - pas de séparateur au-dessus
              rowClasses += ' fonction-group-first';
            } else {
              // Groupes suivants - ligne de séparation en gras
              rowClasses += ' fonction-group-separator';
            }
          }

          // Ajouter la classe pour les stagiaires radiés
          if (trainee.isRadied) {
            rowClasses += ' radied';
            console.log(`🎨 APPLYING RADIED CLASS (GROUPED): ${trainee.nom} ${trainee.prenom} -> classes: "${rowClasses}"`);
          }

          // Préparer les attributs pour le tooltip si le stagiaire est radié
          let tooltipAttributes = '';
          if (trainee.isRadied) {
            const reference = trainee.radiedInfo?.reference;
            const motif = trainee.radiedInfo?.motif;

            let tooltipContent = 'Stagiaire radié';
            if (reference || motif) {
              tooltipContent += '\\n';
              if (reference) tooltipContent += `Référence: ${reference}`;
              if (reference && motif) tooltipContent += '\\n';
              if (motif) tooltipContent += `Motif: ${motif}`;
            } else {
              tooltipContent += '\\nAucune information supplémentaire disponible';
            }

            tooltipAttributes = `data-radied-tooltip="${tooltipContent}"`;
          }

          const htmlRowGrouped = `
            <tr class="${rowClasses}" ${tooltipAttributes}>
              ${index === 0 ? `<td rowspan="${groupedTrainees[fonction].length}" class="fonction-cell">${fonction}</td>` : ''}
              <td>${trainee.grade || '—'}</td>
              <td>${trainee.nom || '—'}</td>
              <td>${trainee.prenom || '—'}</td>
              <td>${trainee.mle || '—'}</td>
              <td>${trainee.unite || '—'}</td>
            </tr>
          `;

          if (trainee.isRadied) {
            console.log(`🔧 HTML GENERATED FOR RADIED TRAINEE (GROUPED): ${trainee.nom} ${trainee.prenom}`);
            console.log(`   HTML: ${htmlRowGrouped.trim()}`);
          }

          tableHTML += htmlRowGrouped;
        });
      });
    } else {
      // Affichage normal (avec ou sans fonction, mais sans groupement)
      filteredTrainees.forEach(trainee => {
        // Déterminer les classes CSS pour la ligne
        const rowClasses = trainee.isRadied ? 'trainee-row radied' : 'trainee-row';

        // Préparer les attributs pour le tooltip si le stagiaire est radié
        let tooltipAttributes = '';
        if (trainee.isRadied) {
          const reference = trainee.radiedInfo?.reference;
          const motif = trainee.radiedInfo?.motif;

          let tooltipContent = 'Stagiaire radié';
          if (reference || motif) {
            tooltipContent += '\\n';
            if (reference) tooltipContent += `Référence: ${reference}`;
            if (reference && motif) tooltipContent += '\\n';
            if (motif) tooltipContent += `Motif: ${motif}`;
          } else {
            tooltipContent += '\\nAucune information supplémentaire disponible';
          }

          tooltipAttributes = `data-radied-tooltip="${tooltipContent}"`;
        }

        const htmlRow = `
          <tr class="${rowClasses}" ${tooltipAttributes}>
            ${hasFonction ? `<td>${trainee.fonction || 'Sans fonction'}</td>` : ''}
            <td>${trainee.grade || '—'}</td>
            <td>${trainee.nom || '—'}</td>
            <td>${trainee.prenom || '—'}</td>
            <td>${trainee.mle || '—'}</td>
            <td>${trainee.unite || '—'}</td>
          </tr>
        `;

        if (trainee.isRadied) {
          console.log(`🔧 HTML GENERATED FOR RADIED TRAINEE: ${trainee.nom} ${trainee.prenom}`);
          console.log(`   HTML: ${htmlRow.trim()}`);
        }

        tableHTML += htmlRow;
      });
    }

    tableHTML += `
        </tbody>
      </table>
    `;

    // Mettre à jour le contenu du conteneur
    traineesContainer.innerHTML = tableHTML;

    // Vérifier si les éléments radiés sont bien dans le DOM
    setTimeout(() => {
      const radiedRows = document.querySelectorAll('.trainee-row.radied');
      console.log(`🔍 FOUND ${radiedRows.length} RADIED ROWS IN DOM`);
      radiedRows.forEach((row, index) => {
        console.log(`   Row ${index + 1}: classes = "${row.className}"`);
        console.log(`   Row ${index + 1}: computed style = background-color: ${getComputedStyle(row).backgroundColor}, text-decoration: ${getComputedStyle(row.querySelector('td')).textDecoration}`);
      });
    }, 100);

    // Ajouter les gestionnaires d'événements pour les tooltips des stagiaires radiés
    setupRadiedTooltips();
  }

  // Variables globales pour stocker les données des stagiaires
  let currentTraineesData = null;
  let currentTraineesFonctions = [];

  // Fonction pour configurer les tooltips des stagiaires radiés
  function setupRadiedTooltips() {
    // Supprimer les tooltips existants
    const existingTooltips = document.querySelectorAll('.radied-tooltip');
    existingTooltips.forEach(tooltip => tooltip.remove());

    // Trouver tous les éléments avec des tooltips de stagiaires radiés
    const radiedRows = document.querySelectorAll('tr[data-radied-tooltip]');

    radiedRows.forEach(row => {
      const tooltipContent = row.getAttribute('data-radied-tooltip');

      // Créer l'élément tooltip
      const tooltip = document.createElement('div');
      tooltip.className = 'radied-tooltip';

      // Diviser le contenu en lignes et créer la structure HTML
      const lines = tooltipContent.split('\\n');
      const titleLine = lines[0];
      const contentLines = lines.slice(1);

      tooltip.innerHTML = `
        <div class="tooltip-title">${titleLine}</div>
        <div class="tooltip-content">${contentLines.join('<br>')}</div>
      `;

      // Ajouter le tooltip au body
      document.body.appendChild(tooltip);

      // Gestionnaires d'événements pour afficher/masquer le tooltip
      row.addEventListener('mouseenter', (e) => {
        const rect = row.getBoundingClientRect();

        // Positionner le tooltip au-dessus de la ligne
        tooltip.style.left = `${rect.left + (rect.width / 2)}px`;
        tooltip.style.top = `${rect.top - 10}px`;
        tooltip.style.transform = 'translateX(-50%) translateY(-100%)';

        // Afficher le tooltip
        tooltip.classList.add('show');
      });

      row.addEventListener('mouseleave', () => {
        // Masquer le tooltip
        tooltip.classList.remove('show');
      });

      // Nettoyer le tooltip quand la ligne est supprimée
      row.addEventListener('DOMNodeRemoved', () => {
        if (tooltip.parentNode) {
          tooltip.parentNode.removeChild(tooltip);
        }
      });
    });
  }

  // Fonction pour filtrer les stagiaires par fonction
  function filterTraineesByFonction(selectedFonction) {
    if (!currentTraineesData) return;

    const { trainees, hasFonction, groupedTrainees } = currentTraineesData;
    renderTraineesTable(trainees, hasFonction, groupedTrainees, selectedFonction);
  }

  // Fonction pour configurer l'observateur de défilement
  function setupScrollObserver() {
    // Nettoyer l'observateur existant s'il y en a un
    if (scrollObserver) {
      scrollObserver.disconnect();
    }

    // Trouver l'élément de la liste des instances
    const instancesList = document.getElementById('instances-list');
    const detailsContainer = document.getElementById('instance-details-container');

    if (!instancesList || !detailsContainer) {
      console.log('Elements not found for scroll observer');
      return;
    }

    // Créer un nouvel observateur d'intersection
    scrollObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const floatingBadge = document.getElementById('floating-tranche-badge');

        if (floatingBadge && currentSelectedInstance) {
          if (entry.isIntersecting) {
            // La liste des instances est visible, masquer le badge flottant
            hideFloatingTranche();
          } else {
            // La liste des instances n'est pas visible, afficher le badge flottant
            showFloatingTranche();
          }
        }
      });
    }, {
      root: detailsContainer, // Observer dans le contexte du conteneur de détails
      threshold: 0.1 // Déclencher quand 10% de l'élément est visible
    });

    // Observer la liste des instances
    scrollObserver.observe(instancesList);
  }

  // Fonction pour afficher la tranche flottante
  function showFloatingTranche() {
    const floatingBadge = document.getElementById('floating-tranche-badge');
    const floatingText = document.getElementById('floating-tranche-text');

    if (floatingBadge && floatingText && currentSelectedInstance) {
      // Calculer la durée de l'instance
      let durationStr = '';
      if (currentSelectedInstance.du && currentSelectedInstance.au) {
        const startDate = currentSelectedInstance.rescheduled_du ?
          new Date(currentSelectedInstance.rescheduled_du) :
          new Date(currentSelectedInstance.du);

        let endDate = new Date(currentSelectedInstance.au);
        if (currentSelectedInstance.rescheduled_au) {
          endDate = new Date(currentSelectedInstance.rescheduled_au);
        } else if (currentSelectedInstance.extended_to) {
          endDate = new Date(currentSelectedInstance.extended_to);
        }

        durationStr = calculateDuration(startDate, endDate);
      }

      // Mettre à jour le texte du badge
      const trancheName = currentSelectedInstance.tranche || `Tranche ${currentSelectedInstance.id}`;
      const trancheText = `Sélectionnée : ${trancheName}${durationStr ? ` • ${durationStr}` : ''}`;
      floatingText.textContent = trancheText;

      // Afficher le badge avec animation
      floatingBadge.style.display = 'flex';
      setTimeout(() => {
        floatingBadge.classList.add('visible');
      }, 10);
    }
  }

  // Fonction pour masquer la tranche flottante
  function hideFloatingTranche() {
    const floatingBadge = document.getElementById('floating-tranche-badge');

    if (floatingBadge) {
      floatingBadge.classList.remove('visible');
      setTimeout(() => {
        floatingBadge.style.display = 'none';
      }, 300); // Attendre la fin de l'animation
    }
  }

  // Fonction pour réinitialiser la tranche flottante
  function resetFloatingTranche() {
    // Nettoyer l'observateur existant
    if (scrollObserver) {
      scrollObserver.disconnect();
      scrollObserver = null;
    }

    // Masquer le badge flottant
    const floatingBadge = document.getElementById('floating-tranche-badge');
    if (floatingBadge) {
      floatingBadge.style.display = 'none';
      floatingBadge.classList.remove('visible');
    }

    // Réinitialiser l'instance sélectionnée
    currentSelectedInstance = null;
  }

  // Fonction pour initialiser les panneaux redimensionnables
  function initResizablePanels() {
    const verticalResizer = document.getElementById('vertical-resizer');
    const leftPanel = document.getElementById('trainings-list-panel');
    const rightPanel = document.getElementById('details-panel');

    if (!verticalResizer || !leftPanel || !rightPanel) {
      console.error('One or more panel elements not found');
      return;
    }

    // État du panneau gauche (visible ou caché)
    let leftPanelCollapsed = false;
    let lastLeftPanelWidth = 30; // Largeur par défaut en pourcentage

    // Fonction pour redimensionner les panneaux verticalement
    function initVerticalResize() {
      let isResizing = false;
      let startX = 0;
      let startLeftWidth = 0;
      let lastUpdateTime = 0;

      verticalResizer.addEventListener('mousedown', (e) => {
        isResizing = true;
        startX = e.clientX;
        startLeftWidth = leftPanel.offsetWidth;

        verticalResizer.classList.add('active');

        // Désactiver les transitions pendant le redimensionnement pour plus de réactivité
        leftPanel.style.transition = 'none';
        rightPanel.style.transition = 'none';
        verticalResizer.style.transition = 'none';

        // Empêcher la sélection de texte pendant le redimensionnement
        document.body.style.userSelect = 'none';
      });

      document.addEventListener('mousemove', (e) => {
        if (!isResizing) return;

        // Throttling pour améliorer les performances (limiter à 60 FPS)
        const now = Date.now();
        if (now - lastUpdateTime < 16) return; // ~60 FPS
        lastUpdateTime = now;

        const containerWidth = leftPanel.parentElement.offsetWidth;
        const newLeftWidth = startLeftWidth + (e.clientX - startX);

        // Seuil de disparition (50px)
        const collapseThreshold = 50;
        const minWidth = 200;
        const maxWidth = Math.min(containerWidth * 0.6, 500); // Max 60% ou 500px maximum

        // Optimisation: éviter les calculs répétitifs
        let targetLeftWidth = newLeftWidth;
        let shouldCollapse = false;

        if (newLeftWidth <= collapseThreshold) {
          shouldCollapse = true;
        } else if (newLeftWidth < minWidth) {
          targetLeftWidth = minWidth;
        } else if (newLeftWidth > maxWidth) {
          targetLeftWidth = maxWidth;
        }

        if (shouldCollapse) {
          // Réduire le panneau gauche à width: 0
          if (!leftPanelCollapsed) {
            leftPanelCollapsed = true;
            leftPanel.style.width = '0px';
            leftPanel.style.minWidth = '0px';
            rightPanel.style.width = '100%';
            verticalResizer.style.left = '0px';
            verticalResizer.style.cursor = 'e-resize';
          }
        } else {
          // Afficher le panneau gauche avec la largeur calculée
          if (leftPanelCollapsed) {
            leftPanelCollapsed = false;
            leftPanel.style.minWidth = '200px';
            verticalResizer.style.left = '';
            verticalResizer.style.cursor = 'col-resize';
          }

          const leftWidthPercent = (targetLeftWidth / containerWidth) * 100;
          const rightWidthPercent = 100 - leftWidthPercent;

          leftPanel.style.width = `${leftWidthPercent}%`;
          rightPanel.style.width = `${rightWidthPercent}%`;
          lastLeftPanelWidth = leftWidthPercent;
        }
      });

      document.addEventListener('mouseup', () => {
        if (isResizing) {
          isResizing = false;
          verticalResizer.classList.remove('active');
          document.body.style.userSelect = '';

          // Réactiver les transitions après le redimensionnement
          leftPanel.style.transition = '';
          rightPanel.style.transition = '';
          verticalResizer.style.transition = '';
        }
      });

      // Double-clic sur le resizer pour basculer entre caché/visible
      verticalResizer.addEventListener('dblclick', () => {
        if (leftPanelCollapsed) {
          // Restaurer le panneau
          leftPanelCollapsed = false;
          leftPanel.style.minWidth = '200px'; // Restore min-width
          leftPanel.style.overflow = 'hidden';
          leftPanel.style.width = `${lastLeftPanelWidth}%`;
          rightPanel.style.width = `${100 - lastLeftPanelWidth}%`;
          verticalResizer.style.left = '';
          verticalResizer.style.cursor = 'col-resize';
        } else {
          // Réduire le panneau à width: 0
          leftPanelCollapsed = true;
          leftPanel.style.width = '0px';
          leftPanel.style.minWidth = '0px'; // Override min-width
          leftPanel.style.overflow = 'hidden';
          rightPanel.style.width = '100%';
          verticalResizer.style.left = '0px';
          verticalResizer.style.cursor = 'e-resize';
        }
      });
    }

    // Initialiser uniquement le redimensionneur vertical
    initVerticalResize();
  }

// Variables globales pour la modal des stagiaires
window.allTraineesData = [];
window.filteredTraineesData = [];

// Fonction globale pour afficher la modal de tous les stagiaires (comme les activités)
window.showAllTraineesModal = function(trainingName) {
  console.log(`Opening all trainees modal for training: ${trainingName}`);

  // Get the modal elements
  const modal = document.getElementById('all-trainees-modal');
  const modalTitle = document.getElementById('all-trainees-modal-title');
  const tableContainer = document.getElementById('all-trainees-table-container');
  const closeModal = document.getElementById('close-all-trainees-modal');

  if (!modal || !modalTitle || !tableContainer) {
    console.error('Modal elements not found');
    return;
  }

  console.log('Opening all trainees modal for', trainingName);

  // Store training name globally for title updates
  window.currentTrainingName = trainingName;

  // Set modal title
  modalTitle.textContent = `Tous les stagiaires - ${trainingName}`;

  // Show loading message
  tableContainer.innerHTML = '<div class="loading-message">Chargement des stagiaires...</div>';

  // Show the modal with animation (like activities modal)
  modal.style.display = 'block';
  document.body.classList.add('modal-open'); // Prevent background scrolling
  setTimeout(() => {
    modal.classList.add('show');
  }, 10);

  // Set up event handlers using the centralized close function
  if (closeModal) {
    closeModal.onclick = closeAllTraineesModal;
  }

  modal.onclick = (e) => {
    if (e.target === modal) {
      closeAllTraineesModal();
    }
  };

  // Handle ESC key
  const handleEscKey = (e) => {
    if (e.key === 'Escape') {
      closeAllTraineesModal();
      document.removeEventListener('keydown', handleEscKey);
    }
  };
  document.addEventListener('keydown', handleEscKey);

  // Load trainees data asynchronously
  setTimeout(async () => {
    try {
      // Récupérer tous les stagiaires de la formation
      const response = await window.api.getAllTraineesForTraining(trainingName);

      if (!response.success) {
        console.error('Error fetching all trainees:', response.error);
        tableContainer.innerHTML = '<div class="error-message">Erreur lors du chargement des stagiaires</div>';
        return;
      }

      window.allTraineesData = response.data;
      window.filteredTraineesData = [...window.allTraineesData];

      console.log(`Retrieved ${window.allTraineesData.length} trainees for training: ${trainingName}`);

      // Rendre la table
      window.renderAllTraineesTable();

      // Configurer les filtres
      window.setupAllTraineesFilters();

    } catch (error) {
      console.error('Error loading all trainees:', error);
      tableContainer.innerHTML = '<div class="error-message">Erreur lors du chargement des stagiaires</div>';
    }
  }, 50);
};

// Fonction globale pour rendre la table de tous les stagiaires
window.renderAllTraineesTable = function() {
  const tableContainer = document.getElementById('all-trainees-table-container');

  if (!tableContainer) {
    console.error('Table container not found');
    return;
  }

  if (window.filteredTraineesData.length === 0) {
    tableContainer.innerHTML = '<div class="placeholder-message">Aucun stagiaire trouvé</div>';
    // Mettre à jour le titre même quand il n'y a pas de résultats
    window.updateModalTitle();
    return;
  }

  // Grouper les stagiaires par instance (tranche) puis par fonction
  const groupedData = {};

  window.filteredTraineesData.forEach(trainee => {
    const instanceKey = trainee.formation_id || 'unknown';
    const instanceLabel = trainee.tranche || `Instance ${trainee.formation_id}`;

    if (!groupedData[instanceKey]) {
      groupedData[instanceKey] = {
        label: instanceLabel,
        dates: {
          du: trainee.du,
          au: trainee.au,
          extended_to: trainee.extended_to,
          rescheduled_du: trainee.rescheduled_du,
          rescheduled_au: trainee.rescheduled_au
        },
        fonctions: {}
      };
    }

    const fonction = trainee.fonction || 'Sans fonction';
    if (!groupedData[instanceKey].fonctions[fonction]) {
      groupedData[instanceKey].fonctions[fonction] = [];
    }

    groupedData[instanceKey].fonctions[fonction].push(trainee);
  });

  // Calculer les rowspans pour les cellules fusionnées
  const instanceRowspans = {};
  const fonctionRowspans = {};

  Object.keys(groupedData).forEach(instanceKey => {
    const instance = groupedData[instanceKey];
    let totalRowsInInstance = 0;

    Object.keys(instance.fonctions).forEach(fonction => {
      const traineesInFunction = instance.fonctions[fonction].length;
      fonctionRowspans[`${instanceKey}-${fonction}`] = traineesInFunction;
      totalRowsInInstance += traineesInFunction;
    });

    instanceRowspans[instanceKey] = totalRowsInInstance;
  });

  // Créer la table avec groupement
  let tableHTML = `
    <table class="all-trainees-table">
      <thead>
        <tr>
          <th>Tranche</th>
          <th>Fonction</th>
          <th>Grade</th>
          <th>Nom</th>
          <th>Prénom</th>
          <th>Unité</th>
        </tr>
      </thead>
      <tbody>
  `;

  // Parcourir les instances groupées
  const instanceKeys = Object.keys(groupedData).sort();
  let instanceIndex = 0;

  instanceKeys.forEach(instanceKey => {
    const instance = groupedData[instanceKey];
    const instanceClass = `instance-${instanceIndex % 8}`;

    // Formater les dates de l'instance
    let datesText = '';
    if (instance.dates.du && instance.dates.au) {
      const duDate = new Date(instance.dates.du).toLocaleDateString('fr-FR');
      let auDate = new Date(instance.dates.au).toLocaleDateString('fr-FR');

      if (instance.dates.extended_to) {
        const extendedDate = new Date(instance.dates.extended_to).toLocaleDateString('fr-FR');
        datesText = `${duDate} - <span class="date-strikethrough">${auDate}</span> <span class="extended-date">${extendedDate}</span>`;
      } else {
        datesText = `${duDate} - ${auDate}`;
      }

      if (instance.dates.rescheduled_du && instance.dates.rescheduled_au) {
        const rescheduledDuDate = new Date(instance.dates.rescheduled_du).toLocaleDateString('fr-FR');
        const rescheduledAuDate = new Date(instance.dates.rescheduled_au).toLocaleDateString('fr-FR');
        datesText = `<span class="date-strikethrough">${duDate} - ${auDate}</span> <span class="extended-date">${rescheduledDuDate} - ${rescheduledAuDate}</span>`;
        datesText += ' <span class="instance-status rescheduled">Reporté</span>';
      } else if (instance.dates.extended_to) {
        datesText += ' <span class="instance-status extended">Prolongé</span>';
      }
    }

    // Parcourir les fonctions dans cette instance
    const fonctionKeys = Object.keys(instance.fonctions).sort();
    let isFirstRowInInstance = true;

    fonctionKeys.forEach(fonction => {
      const trainees = instance.fonctions[fonction];
      let isFirstRowInFunction = true;

      // Lignes des stagiaires
      trainees.forEach((trainee, index) => {
        const rowClasses = `trainee-row ${instanceClass}${trainee.isRadied ? ' radied' : ''}`;

        // Cellule tranche avec rowspan (seulement pour la première ligne de l'instance)
        let trancheCell = '';
        if (isFirstRowInInstance) {
          const instanceRowspan = instanceRowspans[instanceKey];
          trancheCell = `<td rowspan="${instanceRowspan}">${instance.label}${datesText ? `<br><small>${datesText}</small>` : ''}</td>`;
          isFirstRowInInstance = false;
        }

        // Cellule fonction avec rowspan (seulement pour la première ligne de chaque fonction)
        let fonctionCell = '';
        if (isFirstRowInFunction) {
          const fonctionRowspan = fonctionRowspans[`${instanceKey}-${fonction}`];
          fonctionCell = `<td rowspan="${fonctionRowspan}">${trainee.fonction || '—'}</td>`;
          isFirstRowInFunction = false;
        }

        tableHTML += `
          <tr class="${rowClasses}" ${trainee.isRadied ? `data-radied-reference="${trainee.radiedInfo?.reference || ''}" data-radied-motif="${trainee.radiedInfo?.motif || ''}"` : ''}>
            ${trancheCell}
            ${fonctionCell}
            <td>${trainee.grade || '—'}</td>
            <td>${trainee.nom || '—'}</td>
            <td>${trainee.prenom || '—'}</td>
            <td>${trainee.unite || '—'}</td>
          </tr>
        `;
      });
    });

    instanceIndex++;
  });

  tableHTML += `
      </tbody>
    </table>
  `;

  tableContainer.innerHTML = tableHTML;

  // Configurer les tooltips pour les stagiaires radiés
  window.setupAllTraineesRadiedTooltips();

  // Mettre à jour le titre de la modal avec le nombre de stagiaires affichés
  window.updateModalTitle();
};

// Fonction globale pour mettre à jour le titre de la modal avec le nombre de stagiaires
window.updateModalTitle = function() {
  const modalTitle = document.getElementById('all-trainees-modal-title');
  if (!modalTitle) {
    console.log('Modal title element not found');
    return;
  }

  const totalCount = window.allTraineesData ? window.allTraineesData.length : 0;
  const filteredCount = window.filteredTraineesData ? window.filteredTraineesData.length : 0;

  // Récupérer le nom de la formation depuis le titre actuel ou utiliser une variable globale
  const trainingName = window.currentTrainingName || 'Formation';

  console.log('Updating modal title:', {
    trainingName,
    totalCount,
    filteredCount,
    allTraineesData: !!window.allTraineesData,
    filteredTraineesData: !!window.filteredTraineesData
  });

  // Si aucun filtre n'est appliqué (tous les stagiaires sont affichés)
  if (filteredCount === totalCount) {
    modalTitle.innerHTML = `Tous les stagiaires - ${trainingName} - <span class="trainee-counter">${totalCount}</span>`;
  } else {
    // Si des filtres sont appliqués
    modalTitle.innerHTML = `Stagiaires - ${trainingName} - <span class="trainee-counter">${filteredCount} sur ${totalCount}</span>`;
  }

  console.log('Modal title updated to:', modalTitle.textContent);
};

// Fonction globale pour configurer les filtres de la modal
window.setupAllTraineesFilters = function() {
  const searchInput = document.getElementById('all-trainees-search');
  const clearButton = document.getElementById('clear-search-btn');
  const instanceFilter = document.getElementById('all-trainees-instance-filter');
  const fonctionFilter = document.getElementById('all-trainees-fonction-filter');

  if (!searchInput || !clearButton || !instanceFilter || !fonctionFilter) {
    console.error('Filter elements not found');
    return;
  }

  // Remplir le filtre par instance avec des instances uniques
  const uniqueInstances = new Map();

  window.allTraineesData.forEach(trainee => {
    if (trainee.formation_id) {
      const instanceKey = trainee.formation_id;
      const instanceLabel = trainee.tranche || `Instance ${trainee.formation_id}`;

      if (!uniqueInstances.has(instanceKey)) {
        uniqueInstances.set(instanceKey, {
          id: instanceKey,
          label: instanceLabel,
          dates: {
            du: trainee.du,
            au: trainee.au,
            extended_to: trainee.extended_to,
            rescheduled_du: trainee.rescheduled_du,
            rescheduled_au: trainee.rescheduled_au
          }
        });
      }
    }
  });

  // Construire les options du filtre
  instanceFilter.innerHTML = '<option value="">Toutes les instances</option>';

  // Trier les instances par ID
  const sortedInstances = Array.from(uniqueInstances.values()).sort((a, b) => a.id - b.id);

  sortedInstances.forEach(instance => {
    // Formater les dates pour l'affichage
    let dateDisplay = '';
    if (instance.dates.du && instance.dates.au) {
      const duDate = new Date(instance.dates.du).toLocaleDateString('fr-FR');
      const auDate = new Date(instance.dates.au).toLocaleDateString('fr-FR');
      dateDisplay = ` (${duDate} - ${auDate})`;

      if (instance.dates.extended_to) {
        const extendedDate = new Date(instance.dates.extended_to).toLocaleDateString('fr-FR');
        dateDisplay = ` (${duDate} - ${extendedDate} - Prolongé)`;
      }

      if (instance.dates.rescheduled_du && instance.dates.rescheduled_au) {
        const rescheduledDuDate = new Date(instance.dates.rescheduled_du).toLocaleDateString('fr-FR');
        const rescheduledAuDate = new Date(instance.dates.rescheduled_au).toLocaleDateString('fr-FR');
        dateDisplay = ` (${rescheduledDuDate} - ${rescheduledAuDate} - Reporté)`;
      }
    }

    instanceFilter.innerHTML += `<option value="${instance.id}">${instance.label}${dateDisplay}</option>`;
  });

  // Remplir le filtre par fonction
  const fonctions = [...new Set(window.allTraineesData.map(t => t.fonction).filter(f => f && f.trim() !== ''))].sort();
  fonctionFilter.innerHTML = '<option value="">Toutes les fonctions</option>';
  fonctions.forEach(fonction => {
    fonctionFilter.innerHTML += `<option value="${fonction}">${fonction}</option>`;
  });

  // Gestionnaire de recherche instantanée
  searchInput.addEventListener('input', window.filterAllTrainees);

  // Gestionnaire du bouton de nettoyage
  clearButton.addEventListener('click', () => {
    searchInput.value = '';
    instanceFilter.value = '';
    fonctionFilter.value = '';
    window.filterAllTrainees();
  });

  // Gestionnaire du filtre par instance
  instanceFilter.addEventListener('change', window.filterAllTrainees);

  // Gestionnaire du filtre par fonction
  fonctionFilter.addEventListener('change', window.filterAllTrainees);
};

// Fonction globale pour filtrer tous les stagiaires
window.filterAllTrainees = function() {
  const searchInput = document.getElementById('all-trainees-search');
  const instanceFilter = document.getElementById('all-trainees-instance-filter');
  const fonctionFilter = document.getElementById('all-trainees-fonction-filter');

  if (!searchInput || !instanceFilter || !fonctionFilter) return;

  const searchTerm = searchInput.value.toLowerCase().trim();
  const selectedInstance = instanceFilter.value;
  const selectedFonction = fonctionFilter.value;

  window.filteredTraineesData = window.allTraineesData.filter(trainee => {
    // Filtre par instance
    if (selectedInstance && trainee.formation_id != selectedInstance) {
      return false;
    }

    // Filtre par fonction
    if (selectedFonction && trainee.fonction !== selectedFonction) {
      return false;
    }

    // Filtre par recherche textuelle
    if (searchTerm) {
      const searchableText = [
        trainee.nom,
        trainee.prenom,
        trainee.grade,
        trainee.unite,
        trainee.fonction,
        trainee.tranche
      ].filter(field => field).join(' ').toLowerCase();

      if (!searchableText.includes(searchTerm)) {
        return false;
      }
    }

    return true;
  });

  // Re-rendre la table
  window.renderAllTraineesTable();
};

// Fonction globale pour configurer les tooltips des stagiaires radiés dans la modal
window.setupAllTraineesRadiedTooltips = function() {
    const radiedRows = document.querySelectorAll('.all-trainees-table .trainee-row.radied');

    radiedRows.forEach(row => {
      const reference = row.getAttribute('data-radied-reference');
      const motif = row.getAttribute('data-radied-motif');

      if (!reference && !motif) return;

      // Créer le tooltip
      const tooltip = document.createElement('div');
      tooltip.className = 'radied-tooltip';
      tooltip.innerHTML = `
        <div class="tooltip-title">Stagiaire radié</div>
        <div class="tooltip-content">
          ${reference ? `<div><strong>Référence:</strong> ${reference}</div>` : ''}
          ${motif ? `<div><strong>Motif:</strong> ${motif}</div>` : ''}
        </div>
      `;

      document.body.appendChild(tooltip);

      // Gestionnaires d'événements
      row.addEventListener('mouseenter', () => {
        const rect = row.getBoundingClientRect();
        tooltip.style.left = `${rect.left + 10}px`;
        tooltip.style.top = `${rect.top - tooltip.offsetHeight - 5}px`;
        tooltip.classList.add('show');
      });

      row.addEventListener('mouseleave', () => {
        tooltip.classList.remove('show');
      });
    });
  }

// Fonction pour fermer la modal des stagiaires avec gestion du modal-open
function closeAllTraineesModal() {
    const modal = document.getElementById('all-trainees-modal');
    if (modal) {
      modal.classList.remove('show');
      document.body.classList.remove('modal-open'); // Re-enable background scrolling
      setTimeout(() => {
        modal.style.display = 'none';
      }, 300);
    }

    // Nettoyer les données
    window.allTraineesData = [];
    window.filteredTraineesData = [];

    // Nettoyer les tooltips
    const tooltips = document.querySelectorAll('.radied-tooltip');
    tooltips.forEach(tooltip => tooltip.remove());
  }

  // Function to initialize the activities timeline
  async function initActivitiesTimeline() {
    return new Promise(async (resolve) => {
      const activitiesTimeline = document.getElementById('activities-timeline');
      if (!activitiesTimeline) {
        resolve();
        return;
      }

    // Get the parent container
    const timelineContainer = document.querySelector('.activities-timeline-container');

    // Clear the timeline container to prevent duplicates
    activitiesTimeline.innerHTML = '';

    // Remove any existing drag indicator (we'll recreate it)
    const existingDragIndicator = timelineContainer.querySelector('.activities-drag-indicator');
    if (existingDragIndicator) {
      existingDragIndicator.remove();
    }

    // Show loading indicator
    window.showLoading(timelineContainer);

    try {
      // Fetch activities from the database
      const response = await window.api.getActivitiesTimeline();

      // Add a small delay to ensure the loading animation is visible
      await new Promise(resolve => setTimeout(resolve, 800));

      // Hide loading indicator
      window.hideLoading(timelineContainer);

      if (!response.success) {
        console.error('Failed to fetch activities timeline data:', response.error);
        return;
      }

      const activities = response.data;
      console.log(`Received ${activities.length} activities for timeline`);

      // Log the raw activities data
      console.log(`\n========== RAW ACTIVITIES DATA ==========`);
      activities.forEach((activity, index) => {
        console.log(`Activity ${index + 1}:`);
        console.log(`- ID: ${activity.id}`);
        console.log(`- Name: ${activity.activite}`);
        console.log(`- Date (du): ${activity.du} (${typeof activity.du})`);
        console.log(`- Date (au): ${activity.au} (${typeof activity.au})`);
        console.log(`- Source: ${activity.source}`);

        // If du is a Date object, log its properties
        if (activity.du instanceof Date) {
          console.log(`- du as Date: ${activity.du.toISOString()}`);
          console.log(`- du day of week: ${activity.du.getDay()} (0=Sunday, 1=Monday, ..., 6=Saturday)`);
        }

        // Create a new Date object from du and log its properties
        const duDate = new Date(activity.du);
        console.log(`- New Date from du: ${duDate.toISOString()}`);
        console.log(`- New Date day of week: ${duDate.getDay()} (0=Sunday, 1=Monday, ..., 6=Saturday)`);
      });
      console.log(`===========================================\n`);

      // Create control buttons if they don't exist
      let controlsContainer = timelineContainer.querySelector('.timeline-controls');
      if (!controlsContainer) {
        controlsContainer = document.createElement('div');
        controlsContainer.className = 'timeline-controls';
        timelineContainer.appendChild(controlsContainer);

        // Create refresh button
        const refreshButton = document.createElement('button');
        refreshButton.className = 'timeline-control-btn refresh-btn';
        refreshButton.innerHTML = '<i class="fas fa-sync-alt"></i> Actualiser';
        refreshButton.title = 'Actualiser les données';
        refreshButton.addEventListener('click', () => {
          // Add refreshing animation
          refreshButton.classList.add('refreshing');

          // Remove the animation after a delay
          setTimeout(() => {
            refreshButton.classList.remove('refreshing');
          }, 1000);

          // Refresh the statistics (KPIs)
          loadStatistics().then(() => {
            // After statistics are loaded, update the chart with the current period
            if (window.currentComparisonPeriod) {
              console.log('Refreshing with current period from timeline:', window.currentComparisonPeriod);
              // Find the active toggle button and trigger a click on it
              const activeToggleBtn = document.querySelector(`.comparison-toggle-btn[data-period="${window.currentComparisonPeriod}"]`);
              if (activeToggleBtn) {
                // Update the UI to reflect the current period
                document.querySelectorAll('.comparison-toggle-btn').forEach(btn => {
                  btn.classList.remove('active');
                });
                activeToggleBtn.classList.add('active');

                // Move the slider
                const toggleSlider = document.querySelector('.toggle-slider');
                if (toggleSlider) {
                  if (window.currentComparisonPeriod === 'month') {
                    toggleSlider.classList.add('right');
                  } else {
                    toggleSlider.classList.remove('right');
                  }
                }

                // Update the chart with the current period
                updateActivityChart(window.currentComparisonPeriod);
              }
            }
          });

          // Reinitialize the timeline to refresh data
          initActivitiesTimeline().then(() => {
            // After timeline is initialized, scroll to today's activity or marker
            setTimeout(() => {
              // First, try to find today's activity point
              const todayActivityPoint = activitiesTimeline.querySelector('.activity-point.today-activity-point');

              if (todayActivityPoint) {
                // If we found today's activity point, scroll to it
                console.log('Scrolling to today\'s activity point after refresh');
                scrollToElement(activitiesTimeline, todayActivityPoint);
              } else {
                // If no today's activity point, try to find the today marker
                const todayMarker = activitiesTimeline.querySelector('.today-marker');
                if (todayMarker) {
                  console.log('Scrolling to today marker after refresh');
                  scrollToElement(activitiesTimeline, todayMarker);
                }
              }
            }, 200); // Wait a bit longer to ensure the timeline is fully rendered
          });
        });

        // Create scroll to today button
        const todayButton = document.createElement('button');
        todayButton.className = 'timeline-control-btn today-btn';
        todayButton.innerHTML = '<i class="fas fa-calendar-day"></i> Aujourd\'hui';
        todayButton.title = 'Aller à aujourd\'hui';
        todayButton.addEventListener('click', () => {
          // First, try to find today's activity point
          const todayActivityPoint = activitiesTimeline.querySelector('.activity-point.today-activity-point');

          if (todayActivityPoint) {
            // If we found today's activity point, scroll to it
            console.log('Scrolling to today\'s activity point');
            scrollToElement(activitiesTimeline, todayActivityPoint);
          } else {
            // If no today's activity point, try to find the today marker
            const todayMarker = activitiesTimeline.querySelector('.today-marker');
            if (todayMarker) {
              console.log('Scrolling to today marker');
              scrollToElement(activitiesTimeline, todayMarker);
            }
          }
        });

        // Add buttons to controls container
        controlsContainer.appendChild(refreshButton);
        controlsContainer.appendChild(todayButton);
      }

      // Create the timeline line
      const timelineLine = document.createElement('div');
      timelineLine.className = 'activities-timeline-line';

      // Sort activities chronologically by date - use dates as they are
      const sortedActivities = [...activities].sort((a, b) => {
        // Create dates without adding any days
        const dateA = new Date(a.du);
        const dateB = new Date(b.du);

        return dateA - dateB;
      });

      // Group activities by date to handle multiple activities on the same day
      const activitiesByDate = {};
      sortedActivities.forEach(activity => {
        // Create date without adding any days
        const activityDate = new Date(activity.du);

        // Skip invalid dates
        if (isNaN(activityDate.getTime())) {
          console.warn(`Invalid date for activity: ${activity.activite}`);
          return;
        }

        // Format date as YYYY-MM-DD using local date components (ignoring time zone)
        const dateKey = getLocalDateString(activityDate);
        console.log(`Activity: "${activity.activite}", Date: ${activity.du}, Local Date Key: ${dateKey}`);

        // Initialize array if this is the first activity for this date
        if (!activitiesByDate[dateKey]) {
          activitiesByDate[dateKey] = [];
        }

        // Add activity to the array for this date
        activitiesByDate[dateKey].push(activity);
      });

      // Create a new array with unique dates for positioning
      const uniqueDates = Object.keys(activitiesByDate).map(dateKey => {
        // Parse the date components from the YYYY-MM-DD string
        const [year, month, day] = dateKey.split('-').map(num => parseInt(num, 10));

        // Create a date object using local date components
        // month-1 because JavaScript months are 0-indexed
        const date = new Date(year, month-1, day, 0, 0, 0, 0);

        console.log(`Creating date from local date key: ${dateKey} => ${date.toLocaleDateString()}, Day of week: ${date.getDay()}`);

        return {
          date: date,
          activities: activitiesByDate[dateKey]
        };
      });

      console.log(`Grouped ${sortedActivities.length} activities into ${uniqueDates.length} unique dates`);

      // Calculate the total width based on number of activities
      // Make the timeline much wider to ensure it extends far beyond the visible area
      // This creates an "infinite" scrolling effect
      const baseWidth = activitiesTimeline.offsetWidth;

      // Adjust spacing based on the number of activities and container width
      // Use a larger minimum spacing to prevent overlapping
      const minActivitySpacing = 500; // Increased minimum spacing between activities

      // Calculate the total width needed for all unique dates with minimum spacing
      const totalWidthNeeded = uniqueDates.length * minActivitySpacing;

      // Ensure the timeline is at least 3 times the container width or has enough space for all activities
      // Add extra padding on both sides to prevent activities from being cut off
      const sidePadding = 300; // Increased side padding (150px on each side)
      const minWidth = Math.max(baseWidth * 3, totalWidthNeeded + (sidePadding * 2));

      // Set the width of the timeline line
      timelineLine.style.width = `${minWidth}px`;

      // Ensure the line is centered vertically in the container
      timelineLine.style.top = '50%';

      activitiesTimeline.appendChild(timelineLine);

      // Add drag indicator
      const dragIndicator = document.createElement('div');
      dragIndicator.className = 'drag-indicator activities-drag-indicator';
      dragIndicator.innerHTML = `
        <i class="fas fa-arrow-left"></i>
        <i class="fas fa-hand-point-up"></i>
        <i class="fas fa-arrow-right"></i>
      `;
      timelineContainer.appendChild(dragIndicator);

      // Add "Aujourd'hui" marker - always add it regardless of whether there's an activity for today
      addTodayMarker(timelineLine, activities);

      // Set up drag scrolling for the activities timeline
      setupDragScrolling(activitiesTimeline);

      // We'll handle scrolling to today's marker or activity after the timeline is fully initialized
      // This is now done in the .then() callback after initActivitiesTimeline() is called
      console.log("Timeline initialized, scrolling will be handled after initialization");

      // Handle empty activities case - but still show the today marker
      if (activities.length === 0) {
        // Add a message but don't return, so the today marker is still shown
        const emptyMessage = document.createElement('div');
        emptyMessage.style.position = 'absolute';
        emptyMessage.style.top = '50%';
        emptyMessage.style.left = '50%';
        emptyMessage.style.transform = 'translate(-50%, -50%)';
        emptyMessage.style.textAlign = 'center';
        emptyMessage.style.padding = '20px';
        emptyMessage.style.color = 'var(--text-secondary)';
        emptyMessage.style.zIndex = '4';
        emptyMessage.innerHTML = '<p>Aucune activité à afficher</p>';
        activitiesTimeline.appendChild(emptyMessage);
      }

      // For a wider timeline, we'll use pixel-based positioning instead of percentage
      // Calculate the total usable width (total width minus margins on both sides)
      const marginPixels = 300; // Increased side margins to 300px to accommodate past activities
      const totalWidth = timelineLine.offsetWidth;
      const usableWidth = totalWidth - (marginPixels * 2);

      // Define minimum distance between markers (in pixels)
      const minMarkerDistance = 300; // Increased to 300px for better spacing and to prevent overlapping

      // Calculate spacing between unique dates in pixels
      // Ensure it's at least the minimum distance
      let spacing = uniqueDates.length > 1 ? usableWidth / (uniqueDates.length - 1) : 0;

      // If spacing is less than minimum, adjust the timeline width
      if (spacing < minMarkerDistance && uniqueDates.length > 1) {
        // Calculate new width needed for minimum spacing
        const neededWidth = (uniqueDates.length - 1) * minMarkerDistance + (marginPixels * 2);

        // Update timeline width if needed
        if (neededWidth > totalWidth) {
          // Set the new width
          timelineLine.style.width = `${neededWidth}px`;

          // Update variables with new width
          const newTotalWidth = neededWidth;
          const newUsableWidth = newTotalWidth - (marginPixels * 2);
          spacing = newUsableWidth / (uniqueDates.length - 1);

          // Update the totalWidth variable for percentage calculations
          totalWidth = neededWidth;
        }
      }

      // Ensure spacing is never less than the minimum marker distance
      spacing = Math.max(spacing, minMarkerDistance);

      // Process each unique date in chronological order
      uniqueDates.forEach((dateGroup, index) => {
        // Get the date and activities for this group
        const activityDate = dateGroup.date;
        const activitiesForDate = dateGroup.activities;

        // Get the raw date from the first activity in this group (if it exists)
        const rawDate = activitiesForDate && activitiesForDate.length > 0 && activitiesForDate[0] ? activitiesForDate[0].du : null;

        console.log(`\n========== DETAILED DATE PROCESSING IN RENDERER ==========`);
        console.log(`Activity: "${activitiesForDate && activitiesForDate.length > 0 ? activitiesForDate[0].activite : 'N/A'}"`);

        // Log the raw date from the activity
        console.log(`\nRAW DATE FROM ACTIVITY:`);
        console.log(`- Value: ${rawDate}`);
        console.log(`- Type: ${typeof rawDate}`);
        if (rawDate) {
          console.log(`- Constructor: ${rawDate.constructor ? rawDate.constructor.name : 'N/A'}`);

          // If it's a Date object, log detailed information
          if (rawDate instanceof Date) {
            console.log(`\nRAW DATE AS DATE OBJECT:`);
            console.log(`- toString(): ${rawDate.toString()}`);
            console.log(`- toISOString(): ${rawDate.toISOString()}`);
            console.log(`- toUTCString(): ${rawDate.toUTCString()}`);
            console.log(`- toLocaleDateString(): ${rawDate.toLocaleDateString()}`);
            console.log(`- getFullYear(): ${rawDate.getFullYear()}`);
            console.log(`- getMonth(): ${rawDate.getMonth()} (0=January, 11=December)`);
            console.log(`- getDate(): ${rawDate.getDate()}`);
            console.log(`- getDay(): ${rawDate.getDay()} (0=Sunday, 1=Monday, ..., 6=Saturday)`);
            console.log(`- getHours(): ${rawDate.getHours()}`);
            console.log(`- getMinutes(): ${rawDate.getMinutes()}`);
          }
        }

        // Log the activityDate from dateGroup
        console.log(`\nACTIVITY DATE FROM DATEGROUP:`);
        console.log(`- Value: ${activityDate}`);
        console.log(`- Type: ${typeof activityDate}`);
        console.log(`- Constructor: ${activityDate.constructor ? activityDate.constructor.name : 'N/A'}`);
        console.log(`- toString(): ${activityDate.toString()}`);
        console.log(`- toISOString(): ${activityDate.toISOString()}`);
        console.log(`- toUTCString(): ${activityDate.toUTCString()}`);
        console.log(`- toLocaleDateString(): ${activityDate.toLocaleDateString()}`);
        console.log(`- getFullYear(): ${activityDate.getFullYear()}`);
        console.log(`- getMonth(): ${activityDate.getMonth()} (0=January, 11=December)`);
        console.log(`- getDate(): ${activityDate.getDate()}`);
        console.log(`- getDay(): ${activityDate.getDay()} (0=Sunday, 1=Monday, ..., 6=Saturday)`);
        console.log(`- getHours(): ${activityDate.getHours()}`);
        console.log(`- getMinutes(): ${activityDate.getMinutes()}`);
        console.log(`- getTimezoneOffset(): ${activityDate.getTimezoneOffset()} minutes`);

        // Format date with day name and DD.MM
        const dayName = getFrenchDayName(activityDate);
        const formattedDate = `${dayName} ${activityDate.getDate().toString().padStart(2, '0')}.${(activityDate.getMonth() + 1).toString().padStart(2, '0')}`;

        // Log the final formatted date for this activity
        console.log(`\n========== FINAL FORMATTED DATE ==========`);
        console.log(`Activity: "${activitiesForDate && activitiesForDate.length > 0 ? activitiesForDate[0].activite : 'N/A'}"`);
        console.log(`Date object: ${activityDate}`);
        console.log(`Day of week: ${activityDate.getDay()} (${dayName})`);
        console.log(`Formatted date: ${formattedDate}`);
        console.log(`===========================================\n`);

        console.log(`\nFORMATTED DATE FOR DISPLAY:`);
        console.log(`- Day part: ${activityDate.getDate().toString().padStart(2, '0')}`);
        console.log(`- Month part: ${(activityDate.getMonth() + 1).toString().padStart(2, '0')}`);
        console.log(`- Day name: ${dayName}`);
        console.log(`- Formatted date: ${formattedDate}`);
        console.log(`=================================================================\n`);

        // Calculate position with equal spacing in pixels
        // Start at the left margin and add spacing for each unique date
        const positionPixels = uniqueDates.length === 1
          ? totalWidth / 2 // Center if only one date
          : marginPixels + (index * spacing);

        // Convert to percentage of the total width for CSS
        const position = (positionPixels / totalWidth) * 100;

        // Create activity point
        const activityPoint = document.createElement('div');
        activityPoint.className = 'activity-point';

        // Add class if there are multiple activities for this date
        if (activitiesForDate.length > 1) {
          activityPoint.classList.add('multiple-activities');
          // Add data attribute with count for styling
          activityPoint.setAttribute('data-count', activitiesForDate.length);
        }

        // Determine if activity is in the past, today, or upcoming
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison

        // Compare dates using local date strings for accurate comparison
        const activityDateStr = getLocalDateString(activityDate);
        const todayStr = getLocalDateString(today);

        if (activityDateStr === todayStr) {
          // Today's activity - add special class
          activityPoint.classList.add('today-activity-point');
          console.log(`Found activity for today: ${activitiesForDate[0].activite}`);
        } else if (activityDate < today) {
          // Past activity
          activityPoint.classList.add('past');
        } else if (activityDate > today) {
          // Upcoming activity
          activityPoint.classList.add('upcoming');
        }

        activityPoint.style.left = `${position}%`; // This positions the point on the line

        // Create time difference label
        const timeDiffLabel = document.createElement('div');
        const timeDiff = getFormattedTimeDifference(activityDate);

        // Always create the time difference label, even for today
        timeDiffLabel.className = 'time-difference';

        // Add class based on whether it's in the past, today, or future
        if (activityDateStr === todayStr) {
          // Today's activity
          timeDiffLabel.classList.add('today');
        } else {
          const isFuture = activityDate >= new Date();
          timeDiffLabel.classList.add(isFuture ? 'future' : 'past');
        }

        // Add arrow based on direction (no arrow for today)
        const arrowSpan = document.createElement('span');
        if (activityDateStr !== todayStr) {
          const isFuture = activityDate >= new Date();
          arrowSpan.innerHTML = isFuture ? '▶' : '◀'; // Right arrow for future, left arrow for past
          arrowSpan.style.fontSize = '8px';
        }

        // Create text span
        const textSpan = document.createElement('span');
        textSpan.textContent = timeDiff;

        // Add elements to label
        timeDiffLabel.appendChild(arrowSpan);
        timeDiffLabel.appendChild(textSpan);

        // Create date label
        const dateLabel = document.createElement('div');
        dateLabel.className = 'activity-date';
        dateLabel.textContent = formattedDate;

        // If there are multiple activities, add a count indicator to the date label
        if (activitiesForDate.length > 1) {
          const countBadge = document.createElement('span');
          countBadge.className = 'activity-count-badge';
          countBadge.textContent = activitiesForDate.length;
          dateLabel.appendChild(countBadge);
        }

        // Create vertical line
        const verticalLine = document.createElement('div');
        verticalLine.className = 'activity-vertical-line';
        verticalLine.style.left = '50%'; // Center with the point

        // For multiple activities, make the line taller
        if (activitiesForDate.length > 1) {
          // Increase height based on number of activities
          const dotsHeight = activitiesForDate.length * 16; // 8px for dot + 8px for gap
          verticalLine.style.height = `${Math.max(80, dotsHeight + 40)}px`; // Ensure minimum height of 80px to accommodate lieu
        }

        // Create activity names container for multiple activities
        const namesContainer = document.createElement('div');
        namesContainer.className = 'activity-names-container';

        // Process each activity for this date
        activitiesForDate.forEach((activity, activityIndex) => {
          // Create activity name
          const nameLabel = document.createElement('div');
          nameLabel.className = 'activity-name';
          nameLabel.setAttribute('data-activity-index', activityIndex);

          // For multiple activities, add index class and hide all except the first one
          if (activitiesForDate.length > 1) {
            nameLabel.classList.add(`activity-${activityIndex + 1}`);
            // Only show the first activity initially
            if (activityIndex > 0) {
              nameLabel.style.display = 'none';
            }
          }

          nameLabel.textContent = activity.activite || 'Activité';

          // Create lieu element
          const lieuLabel = document.createElement('div');
          lieuLabel.className = 'activity-lieu';
          lieuLabel.setAttribute('data-activity-index', activityIndex);

          // For multiple activities, add index class and hide all except the first one
          if (activitiesForDate.length > 1) {
            lieuLabel.classList.add(`activity-lieu-${activityIndex + 1}`);
            // Only show the first activity's lieu initially
            if (activityIndex > 0) {
              lieuLabel.style.display = 'none';
            }
          }

          // Add location icon and text
          lieuLabel.innerHTML = `<i class="fas fa-map-marker-alt"></i> ${activity.lieu || 'Lieu non spécifié'}`;

          // Add tooltip with full details
          nameLabel.title = `${activity.activite}\nDate: ${formatDate(activityDate)}\nLieu: ${activity.lieu || 'Non spécifié'}\nSource: ${activity.source}`;

          // Add both elements to container
          namesContainer.appendChild(nameLabel);
          namesContainer.appendChild(lieuLabel);
        });

        // If there are multiple activities, add dots navigation
        if (activitiesForDate.length > 1) {
          // Create dots container
          const dotsContainer = document.createElement('div');
          dotsContainer.className = 'activity-dots';

          // Create dots array to manage visibility
          const dots = [];

          // Create a dot for each activity
          activitiesForDate.forEach((_, activityIndex) => {
            const dot = document.createElement('div');
            dot.className = 'activity-dot';
            dot.setAttribute('data-activity-index', activityIndex);

            // Add number to dot (1-based for user display)
            dot.setAttribute('data-number', activityIndex + 1);

            // Make the first dot active
            if (activityIndex === 0) {
              dot.classList.add('active');
            }

            // Initially hide dots beyond the first 3
            if (activityIndex >= 3) {
              dot.style.display = 'none';
            }

            // Add click event to switch between activities
            dot.addEventListener('click', (e) => {
              e.stopPropagation(); // Prevent event bubbling

              // Get the activity index from the dot
              const index = parseInt(dot.getAttribute('data-activity-index'));

              // Hide all activity names and lieu labels
              const activityNames = namesContainer.querySelectorAll('.activity-name');
              const activityLieux = namesContainer.querySelectorAll('.activity-lieu');

              activityNames.forEach(name => {
                name.style.display = 'none';
              });

              activityLieux.forEach(lieu => {
                lieu.style.display = 'none';
              });

              // Show the selected activity name and lieu
              const selectedName = namesContainer.querySelector(`.activity-name[data-activity-index="${index}"]`);
              const selectedLieu = namesContainer.querySelector(`.activity-lieu[data-activity-index="${index}"]`);

              if (selectedName) {
                selectedName.style.display = 'block';
              }

              if (selectedLieu) {
                selectedLieu.style.display = 'block';
              }

              // Update active dot
              const allDots = dotsContainer.querySelectorAll('.activity-dot');
              allDots.forEach(d => {
                d.classList.remove('active');
              });
              dot.classList.add('active');

              // Implement scrolling effect for dots if more than 3
              if (activitiesForDate.length > 3) {
                // Calculate which dots should be visible
                let startDot = Math.max(0, index - 1); // Try to keep selected dot in the middle
                let endDot = startDot + 2; // Show 3 dots

                // Adjust if we're at the beginning or end
                if (endDot >= activitiesForDate.length) {
                  endDot = activitiesForDate.length - 1;
                  startDot = Math.max(0, endDot - 2);
                }

                // Hide all dots first
                allDots.forEach(d => {
                  d.style.display = 'none';
                });

                // Show only the dots in the visible range
                for (let i = startDot; i <= endDot; i++) {
                  allDots[i].style.display = 'flex';
                }

                // Update more indicators
                dotsContainer.classList.remove('has-more-above', 'has-more-below');

                // Show more above indicator if there are hidden dots above
                if (startDot > 0) {
                  dotsContainer.classList.add('has-more-above');
                }

                // Show more below indicator if there are hidden dots below
                if (endDot < activitiesForDate.length - 1) {
                  dotsContainer.classList.add('has-more-below');
                }
              }
            });

            dots.push(dot);
            dotsContainer.appendChild(dot);
          });

          // Add more indicators if there are more than 3 activities
          if (activitiesForDate.length > 3) {
            // Initially show the more below indicator since we're showing the first 3 dots
            dotsContainer.classList.add('has-more-below');
          }

          // Add dots container to activity point
          activityPoint.appendChild(dotsContainer);
        }

        // Add click event to show detailed modal
        activityPoint.addEventListener('click', () => {
          // Show activity details modal with all activities for this date
          console.log('Activity point clicked, showing modal for', activitiesForDate);
          window.showActivityDetailsModal(activitiesForDate, 0);
        });

        // Add elements to timeline
        activityPoint.appendChild(timeDiffLabel);
        activityPoint.appendChild(dateLabel);
        activityPoint.appendChild(verticalLine);
        activityPoint.appendChild(namesContainer);
        timelineLine.appendChild(activityPoint);
      });
    } catch (error) {
      console.error('Error initializing activities timeline:', error);
      window.hideLoading(timelineContainer);

      // Display error message
      activitiesTimeline.innerHTML = `
        <div style="text-align: center; padding: 20px; color: var(--text-secondary);">
          <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i>
          <p>Erreur lors du chargement des activités.</p>
        </div>
      `;
      resolve(); // Resolve the promise even in case of error
    }

    // Resolve the promise after everything is done
    resolve();
    });
  }

  // Function to fetch and display training schedules
  async function fetchTrainingSchedules(timelineEvents, firstDayOfYear) {
    // Show loading indicator
    const planningContainer = document.querySelector('.planning-schedule-container');
    window.showLoading(planningContainer); // No need to store the reference

    try {
      // Fetch training schedules from the database
      const response = await window.api.getTrainingSchedules();

      // Add a small delay to ensure the loading animation is visible
      await new Promise(resolve => setTimeout(resolve, 800));

      // Hide loading indicator
      window.hideLoading(planningContainer);

      if (!response.success) {
        console.error('Failed to fetch training schedules:', response.error);
        return;
      }

      const schedules = response.data;
      console.log(`Received ${schedules.length} current training schedules`);

      // Determine how many event rows we need - create rows for all trainings
      const numRows = schedules.length === 0 ? 1 : schedules.length;
      console.log(`Creating ${numRows} event row(s) for ${schedules.length} training(s)`);

      // Create the appropriate number of event rows
      for (let i = 0; i < numRows; i++) {
        const eventRow = document.createElement('div');
        eventRow.className = 'event-row';
        eventRow.setAttribute('data-row', i);
        timelineEvents.appendChild(eventRow);
      }

      // Get the created event rows
      const eventRows = timelineEvents.querySelectorAll('.event-row');

      if (schedules.length === 0) {
        console.log('No current training schedules found');

        // Ensure we have at least one event row
        if (eventRows.length === 0) {
          console.log('Creating a new event row for the placeholder');
          const eventRow = document.createElement('div');
          eventRow.className = 'event-row';
          eventRow.setAttribute('data-row', 0);
          timelineEvents.appendChild(eventRow);

          // Get the updated event rows
          const updatedEventRows = timelineEvents.querySelectorAll('.event-row');
          if (updatedEventRows.length > 0) {
            // Create a placeholder that will be positioned at the current week
            createCurrentWeekPlaceholder(updatedEventRows[0], firstDayOfYear);
            console.log('Placeholder added to event row at current week position');
          }
        } else {
          // Create a placeholder that will be positioned at the current week
          createCurrentWeekPlaceholder(eventRows[0], firstDayOfYear);
          console.log('Placeholder added to existing event row at current week position');
        }

        // Update the fixed training count element for zero trainings
        const activeTrainingsCount = document.getElementById('active-trainings-count');
        if (activeTrainingsCount) {
          // Create a container for the message and icon
          activeTrainingsCount.innerHTML = '';

          // Add an appropriate icon
          const icon = document.createElement('i');
          icon.className = 'fas fa-exclamation-circle';
          icon.style.marginRight = '8px';
          activeTrainingsCount.appendChild(icon);

          // Add the message text
          const messageText = document.createTextNode('Il n\'y a aucune formation en cours !');
          activeTrainingsCount.appendChild(messageText);

          console.log('Updated training count to show no trainings message');
        } else {
          console.error('Could not find active-trainings-count element');
        }

        return;
      }

      // Helper function to create and position a placeholder at the current week
      function createCurrentWeekPlaceholder(eventRow, firstDayOfYear) {
        // Create a placeholder with the same shape as the timeline
        const noTrainingsPlaceholder = document.createElement('div');
        noTrainingsPlaceholder.className = 'no-trainings-placeholder training-event';

        // Add an appropriate icon
        const icon = document.createElement('i');
        icon.className = 'fas fa-graduation-cap';
        icon.style.marginRight = '8px';
        noTrainingsPlaceholder.appendChild(icon);

        // Add the message text
        const messageText = document.createElement('span');
        messageText.textContent = 'Aucune formation n\'est en cours';
        noTrainingsPlaceholder.appendChild(messageText);

        // Calculate the position for the current week
        const today = new Date();
        const dayWidth = 65; // Width of each day column

        // Calculate the first Monday of the year
        const firstDayOfYearDayOfWeek = firstDayOfYear.getDay();
        let daysToFirstMonday = 1 - firstDayOfYearDayOfWeek; // If Jan 1 is Monday (1), add 0 days
        if (daysToFirstMonday > 1) daysToFirstMonday -= 7; // If Jan 1 is Sat/Sun, go back to previous Monday

        // Calculate the first Monday of the year
        const firstMondayOfYear = new Date(firstDayOfYear);
        firstMondayOfYear.setDate(firstDayOfYear.getDate() + daysToFirstMonday);

        // Calculate days from first Monday to today
        const diffDays = Math.floor((today - firstMondayOfYear) / (24 * 60 * 60 * 1000));

        // Calculate the week number (0-indexed)
        const weekNumber = Math.floor(diffDays / 7);

        // Get day of the week for today (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
        const todayDayOfWeek = today.getDay();

        // Convert to our index (0 = Monday, 1 = Tuesday, ..., 4 = Friday)
        // If it's a weekend, use Friday (4)
        const dayIndex = todayDayOfWeek === 0 || todayDayOfWeek === 6 ? 4 : todayDayOfWeek - 1;

        // Calculate the column position
        const columnPosition = weekNumber * 5 + dayIndex;

        // Calculate left position
        const left = columnPosition * dayWidth;

        // Set position and make it a training-event to match the style
        noTrainingsPlaceholder.style.left = `${left}px`;
        noTrainingsPlaceholder.style.width = `${dayWidth * 3}px`; // Make it span 3 days

        // Add the placeholder to the event row
        eventRow.appendChild(noTrainingsPlaceholder);
      }

      // Process each training schedule
      schedules.forEach((training, index) => {
        // Parse dates
        const startDate = new Date(training.du);
        const endDate = new Date(training.au);

        // Skip invalid dates
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          console.warn(`Invalid date for training: ${training.nom_formation}`);
          return;
        }

        console.log(`Processing training: ${training.nom_formation}, Start: ${startDate.toISOString()}, End: ${endDate.toISOString()}`);

        // Calculate position and width
        const dayWidth = 65; // Width of each day column (further reduced to 65px)

        // Get day of the week for start date (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
        const startDayOfWeek = startDate.getDay();
        const endDayOfWeek = endDate.getDay();

        // Adjust for weekends - we only show Monday (1) to Friday (5)
        // If it's a weekend, adjust to the next Monday or previous Friday
        let adjustedStartDate = new Date(startDate);
        let adjustedEndDate = new Date(endDate);

        // For start date: if weekend, move to Monday
        if (startDayOfWeek === 0) { // Sunday
          adjustedStartDate.setDate(adjustedStartDate.getDate() + 1); // Move to Monday
        } else if (startDayOfWeek === 6) { // Saturday
          adjustedStartDate.setDate(adjustedStartDate.getDate() + 2); // Move to Monday
        }

        // For end date: if weekend, move to Friday
        if (endDayOfWeek === 0) { // Sunday
          adjustedEndDate.setDate(adjustedEndDate.getDate() - 2); // Move to Friday
        } else if (endDayOfWeek === 6) { // Saturday
          adjustedEndDate.setDate(adjustedEndDate.getDate() - 1); // Move to Friday
        }

        // Calculate days from start of year (considering only weekdays)
        // We need to calculate the exact column position

        // Calculate the first Monday of the year (same as in initPlanningSchedule)
        const firstDayOfYearDayOfWeek = firstDayOfYear.getDay();
        let daysToFirstMonday = 1 - firstDayOfYearDayOfWeek; // If Jan 1 is Monday (1), add 0 days
        if (daysToFirstMonday > 1) daysToFirstMonday -= 7; // If Jan 1 is Sat/Sun, go back to previous Monday

        // Calculate the first Monday of the year
        const firstMondayOfYear = new Date(firstDayOfYear);
        firstMondayOfYear.setDate(firstDayOfYear.getDate() + daysToFirstMonday);

        // Calculate the week number based on the first Monday of the year
        // This ensures consistency with the week numbers displayed in the timeline

        // Calculate the difference in days between the dates and the first Monday of the year
        const startDiffDays = Math.floor((adjustedStartDate - firstMondayOfYear) / (24 * 60 * 60 * 1000));
        const endDiffDays = Math.floor((adjustedEndDate - firstMondayOfYear) / (24 * 60 * 60 * 1000));

        // Calculate the week number (1-indexed)
        // Week 1 is the week containing the first Monday of the year
        const startWeek = Math.floor(startDiffDays / 7) + 1;
        const endWeek = Math.floor(endDiffDays / 7) + 1;

        console.log(`First Monday of year: ${firstMondayOfYear.toDateString()}`);
        console.log(`Adjusted start date: ${adjustedStartDate.toDateString()}, Adjusted end date: ${adjustedEndDate.toDateString()}`);
        console.log(`Start week (from first Monday): ${startWeek}, End week (from first Monday): ${endWeek}`);

        // Then, get the day within the week (0-4 for Monday-Friday)
        // Convert from JavaScript's day of week (0=Sunday, 1=Monday) to our index (0=Monday, 1=Tuesday)
        const startDayIndex = adjustedStartDate.getDay() === 0 ? 4 : adjustedStartDate.getDay() - 1;
        const endDayIndex = adjustedEndDate.getDay() === 0 ? 4 : adjustedEndDate.getDay() - 1;

        // Calculate the exact column position
        // We need to adjust the week number to be 0-indexed for the calculation
        // Subtract 1 from the week number to make it 0-indexed
        const startColumn = (startWeek - 1) * 5 + startDayIndex; // 5 days per week
        const endColumn = (endWeek - 1) * 5 + endDayIndex;

        console.log(`Original start column: ${startColumn}, Original end column: ${endColumn}`);

        console.log(`Training: ${training.nom_formation}, Start Week: ${startWeek}, Start Day: ${startDayIndex}, End Week: ${endWeek}, End Day: ${endDayIndex}`);
        console.log(`Start Column: ${startColumn}, End Column: ${endColumn}`);

        // Calculate left position and width
        const left = startColumn * dayWidth;
        const width = (endColumn - startColumn + 1) * dayWidth - 10; // -10 for padding

        // Create training event element
        const trainingEvent = document.createElement('div');
        trainingEvent.className = 'training-event';

        // Create a more detailed tooltip that shows this is a current training
        const today = new Date();
        const daysActive = Math.floor((today - startDate) / (24 * 60 * 60 * 1000)) + 1;
        const totalDays = Math.floor((endDate - startDate) / (24 * 60 * 60 * 1000)) + 1;

        trainingEvent.title = `${training.nom_formation} (${training.domaine || 'Non spécifié'})\nDu: ${formatDate(startDate)}\nAu: ${formatDate(endDate)}\nJour ${daysActive}/${totalDays} de la formation`;

        // Set position and size
        trainingEvent.style.left = `${left}px`;
        trainingEvent.style.width = `${width}px`;

        // Set data attributes
        trainingEvent.setAttribute('data-id', training.id);
        trainingEvent.setAttribute('data-domain', training.domaine || 'Autre');

        // Create a container for the repeating pattern of names
        const namePattern = document.createElement('div');
        namePattern.className = 'training-name-pattern';

        // Calculate how many times we need to repeat the name
        // We'll add extra repetitions to ensure it covers the entire width
        const trainingName = training.nom_formation;
        const avgCharWidth = 6; // Approximate width of a character in pixels
        const nameWidth = trainingName.length * avgCharWidth;

        // Calculate how many repetitions we need to fill the width completely
        // Account for the vertical bar separator (3 characters: space, |, space)
        const separatorWidth = 3 * avgCharWidth;
        // Multiply by 4 to ensure there are absolutely no gaps during animation
        const repetitions = Math.ceil(width / (nameWidth + separatorWidth)) * 4;

        // Calculate training duration in weeks
        const durationInMs = endDate.getTime() - startDate.getTime();
        const durationInDays = Math.ceil(durationInMs / (24 * 60 * 60 * 1000));
        const durationInWeeks = Math.ceil(durationInDays / 7);

        // Format the duration with leading zeros
        const formattedDuration = durationInWeeks.toString().padStart(2, '0');
        // Use singular form "semaine" if duration is exactly 1 week
        const durationLabel = durationInWeeks === 1
          ? `${formattedDuration} semaine`
          : `${formattedDuration} semaines`;

        // Create the repeating pattern with vertical bar separators and duration label
        namePattern.innerHTML = ''; // Use innerHTML instead of textContent to add HTML elements

        for (let i = 0; i < repetitions; i++) {
          // Create a span for the training name
          const nameSpan = document.createElement('span');
          nameSpan.textContent = trainingName;
          namePattern.appendChild(nameSpan);

          // Add separator
          namePattern.appendChild(document.createTextNode(' | '));

          // Create a span for the duration with special styling
          const durationSpan = document.createElement('span');
          durationSpan.className = 'training-duration';
          durationSpan.textContent = durationLabel;
          namePattern.appendChild(durationSpan);

          // Add separator
          namePattern.appendChild(document.createTextNode(' | '));
        }

        trainingEvent.appendChild(namePattern);

        // Alternate between rows
        const rowIndex = index % eventRows.length;
        eventRows[rowIndex].appendChild(trainingEvent);
      });

      // Update the fixed training count element
      const activeTrainingsCount = document.getElementById('active-trainings-count');
      if (activeTrainingsCount) {
        // Format the number with leading zero if needed
        const formattedCount = schedules.length.toString().padStart(2, '0');

        // Set the text based on the number of trainings
        if (schedules.length === 1) {
          activeTrainingsCount.textContent = `${formattedCount} formation en cours`;
        } else {
          activeTrainingsCount.textContent = `${formattedCount} formations en cours`;
        }

        console.log(`Updated training count to: ${activeTrainingsCount.textContent}`);
      } else {
        console.error('Could not find active-trainings-count element');
      }
    } catch (error) {
      console.error('Error displaying training schedules:', error);
    }
  }

  // Helper function to format date
  function formatDate(date) {
    // French month names
    const frenchMonths = [
      'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
      'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
    ];

    return `${date.getDate()} ${frenchMonths[date.getMonth()]} ${date.getFullYear()}`;
  }

  // Helper function to get French day name
  function getFrenchDayName(date) {
    const frenchDays = [
      'Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'
    ];

    return frenchDays[date.getDay()];
  }

  // Helper function to format date with day name
  function formatDateWithDay(date) {
    const dayName = getFrenchDayName(date);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${dayName} ${day}.${month}.${year}`;
  }

  // Helper function to get a consistent local date string (YYYY-MM-DD)
  // This ignores time zone issues by using local date components
  function getLocalDateString(date) {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }

  // Helper function to calculate and format time difference between dates
  function getFormattedTimeDifference(targetDate, referenceDate = new Date()) {
    // Set both dates to beginning of day for accurate day calculation
    const target = new Date(targetDate);
    target.setHours(0, 0, 0, 0);

    const reference = new Date(referenceDate);
    reference.setHours(0, 0, 0, 0);

    // Calculate difference in milliseconds
    const diffMs = target.getTime() - reference.getTime();
    const isFuture = diffMs >= 0;

    // Convert to days
    const diffDays = Math.abs(Math.floor(diffMs / (1000 * 60 * 60 * 24)));

    // If it's today, return special message
    if (diffDays === 0) {
      return "Aujourd'hui";
    }

    // If less than a month (30 days)
    if (diffDays < 30) {
      return isFuture
        ? `Dans ${diffDays} ${diffDays === 1 ? 'jr' : 'jrs'}`
        : `Il y a ${diffDays} ${diffDays === 1 ? 'jr' : 'jrs'}`;
    }

    // Calculate months and remaining days
    const months = Math.floor(diffDays / 30);
    const remainingDays = diffDays % 30;

    // If less than a year
    if (months < 12) {
      let result = isFuture ? `Dans ${months} ${months === 1 ? 'mois' : 'mois'}` : `Il y a ${months} ${months === 1 ? 'mois' : 'mois'}`;

      // Add days if there are any
      if (remainingDays > 0) {
        result += ` ${remainingDays} ${remainingDays === 1 ? 'jr' : 'jrs'}`;
      }

      return result;
    }

    // Calculate years, remaining months
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;

    let result = isFuture
      ? `Dans ${years} ${years === 1 ? 'an' : 'ans'}`
      : `Il y a ${years} ${years === 1 ? 'an' : 'ans'}`;

    // Add months if there are any
    if (remainingMonths > 0) {
      result += ` ${remainingMonths} ${remainingMonths === 1 ? 'mois' : 'mois'}`;
    }

    return result;
  }

  // Function to set up drag scrolling for a container
  function setupDragScrolling(container) {
    if (!container) return;

    let isDown = false;
    let startX;
    let scrollLeft;

    // Mouse events
    container.addEventListener('mousedown', (e) => {
      isDown = true;
      container.style.cursor = 'grabbing';
      startX = e.pageX - container.offsetLeft;
      scrollLeft = container.scrollLeft;
      e.preventDefault();
    });

    container.addEventListener('mouseleave', () => {
      isDown = false;
      container.style.cursor = 'grab';
    });

    container.addEventListener('mouseup', () => {
      isDown = false;
      container.style.cursor = 'grab';
    });

    container.addEventListener('mousemove', (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - container.offsetLeft;
      const walk = (x - startX) * 2; // Scroll speed multiplier
      container.scrollLeft = scrollLeft - walk;
    });

    // Set initial cursor style
    container.style.cursor = 'grab';

    // Add wheel event for horizontal scrolling
    container.addEventListener('wheel', (e) => {
      e.preventDefault();
      container.scrollLeft += e.deltaY;
    });
  }

  // Function to scroll to any element in a horizontal container
  function scrollToElement(container, element) {
    if (!container || !element) return;

    // Get the position of the element relative to the container
    const elementLeft = element.offsetLeft;

    // Calculate the position to scroll to (center the element)
    const scrollPosition = elementLeft - (container.offsetWidth / 2) + (element.offsetWidth / 2);

    // Scroll to the position with smooth animation
    container.scrollTo({
      left: scrollPosition,
      behavior: 'smooth'
    });

    console.log(`Scrolling to element at position ${elementLeft}, scroll position: ${scrollPosition}`);
  }

  // Function to add "Aujourd'hui" marker to the timeline
  function addTodayMarker(timelineLine, activities) {
    // Get today's date
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison

    // Format date with day name and DD.MM
    const dayName = getFrenchDayName(today);
    const todayFormattedDate = `${dayName} ${today.getDate().toString().padStart(2, '0')}.${(today.getMonth() + 1).toString().padStart(2, '0')}`;
    console.log(`Today formatted date: ${todayFormattedDate}`);

    // Get today's date in YYYY-MM-DD format using local date components
    const todayStr = getLocalDateString(today);

    console.log(`Checking if today (${todayStr}) exists in the database using local date string...`);

    // Check if there's an activity for today
    let hasTodayActivity = false;
    let todayActivity = null;

    // Check if any activity has today's date using UTC date strings for consistent comparison
    activities.forEach(activity => {
      // Create date without adding any days
      const activityDate = new Date(activity.du);

      // Format date as YYYY-MM-DD using local date components for consistent comparison
      const activityDateStr = getLocalDateString(activityDate);

      console.log(`Comparing activity date: ${activity.activite}, Local date: ${activityDateStr} with today: ${todayStr}`);

      if (activityDateStr === todayStr) {
        hasTodayActivity = true;
        todayActivity = activity;
        console.log(`Found activity for today in database: ${activity.activite}, UTC date: ${activityDateStr}`);
      }
    });

    // If there's an activity for today in the main timeline, don't add the today marker
    // to avoid duplication
    if (hasTodayActivity) {
      console.log("Activity for today exists in the main timeline, skipping today marker");
      return null;
    }

    console.log("Adding today marker to the timeline");

    // Default position (middle if no activities)
    let position = 50;

    if (activities.length > 0) {
      // Sort activities chronologically - use dates as they are
      const sortedActivities = [...activities].sort((a, b) => {
        // Create dates without adding any days
        const dateA = new Date(a.du);
        const dateB = new Date(b.du);

        return dateA - dateB;
      });

      // Group activities by date to handle multiple activities on the same day
      const activitiesByDate = {};
      sortedActivities.forEach(activity => {
        // Create date without adding any days
        const activityDate = new Date(activity.du);

        // Skip invalid dates
        if (isNaN(activityDate.getTime())) {
          return;
        }

        // Format date as YYYY-MM-DD using local date components for consistent grouping
        const dateKey = getLocalDateString(activityDate);
        console.log(`Today marker - Activity: "${activity.activite}", Local Date Key: ${dateKey}`);

        // Initialize array if this is the first activity for this date
        if (!activitiesByDate[dateKey]) {
          activitiesByDate[dateKey] = [];
        }

        // Add activity to the array for this date
        activitiesByDate[dateKey].push(activity);
      });

      // Create a new array with unique dates for positioning
      const uniqueDates = Object.keys(activitiesByDate).map(dateKey => {
        // Parse the date components from the YYYY-MM-DD string
        const [year, month, day] = dateKey.split('-').map(num => parseInt(num, 10));

        // Create a date object using local date components
        // month-1 because JavaScript months are 0-indexed
        const date = new Date(year, month-1, day, 0, 0, 0, 0);

        console.log(`Today marker - Creating date from local date key: ${dateKey} => ${date.toLocaleDateString()}, Day of week: ${date.getDay()}`);

        return {
          date: date,
          activities: activitiesByDate[dateKey]
        };
      }).sort((a, b) => a.date - b.date);

      // For a wider timeline, we'll use pixel-based positioning
      const marginPixels = 300; // Increased to 300px to match the main timeline
      const totalWidth = timelineLine.offsetWidth;
      const usableWidth = totalWidth - (marginPixels * 2);

      // Define minimum distance between markers (in pixels) - same as in main function
      const minMarkerDistance = 300; // Increased to 300px to match the main timeline

      // Calculate spacing between unique dates in pixels
      let spacing = uniqueDates.length > 1 ? usableWidth / (uniqueDates.length - 1) : 0;

      // If spacing is less than minimum, use the minimum distance
      if (spacing < minMarkerDistance && uniqueDates.length > 1) {
        spacing = minMarkerDistance;
      }

      // Ensure spacing is never less than the minimum marker distance
      spacing = Math.max(spacing, minMarkerDistance);

      // Find where today fits in the timeline

      // Find the position for today's marker
      // First, identify which activities are in the past, today, and future
      const pastDates = [];
      const futureDates = [];

      uniqueDates.forEach(dateGroup => {
        const date = dateGroup.date;

        // Compare dates using only year, month, and day components
        const dateStr = getLocalDateString(date);
        const todayStr = getLocalDateString(today);

        console.log(`Comparing date ${dateStr} with today ${todayStr}`);

        if (dateStr < todayStr) {
          pastDates.push(dateGroup);
        } else if (dateStr > todayStr) {
          futureDates.push(dateGroup);
        }
      });

      console.log(`Found ${pastDates.length} past dates and ${futureDates.length} future dates`);

      // Position the today marker based on past and future activities
      if (pastDates.length === 0 && futureDates.length > 0) {
        // All activities are in the future, position today marker at the beginning
        const firstFuturePos = marginPixels + (0 * spacing);
        const positionPixels = firstFuturePos - minMarkerDistance; // Position before the first future activity
        position = (positionPixels / totalWidth) * 100;
        console.log(`Positioning today marker before all future activities at ${position}%`);
      }
      else if (pastDates.length > 0 && futureDates.length === 0) {
        // All activities are in the past, position today marker at the end
        const lastPastPos = marginPixels + ((pastDates.length - 1) * spacing);
        const positionPixels = lastPastPos + minMarkerDistance; // Position after the last past activity
        position = (positionPixels / totalWidth) * 100;
        console.log(`Positioning today marker after all past activities at ${position}%`);
      }
      else if (pastDates.length > 0 && futureDates.length > 0) {
        // Activities on both sides, position today marker between the last past and first future
        const lastPastPos = marginPixels + ((pastDates.length - 1) * spacing);
        const firstFuturePos = marginPixels + (pastDates.length * spacing);

        // Position exactly in the middle between the last past and first future
        const positionPixels = (lastPastPos + firstFuturePos) / 2;
        position = (positionPixels / totalWidth) * 100;
        console.log(`Positioning today marker between past and future activities at ${position}%`);
      }
      else {
        // No activities or only activities for today, use default position (middle)
        position = 50;
        console.log(`Using default position for today marker at ${position}%`);
      }

      // We've already set the position in the code above, so we don't need this fallback anymore
    }

    // Create today marker container
    const todayMarker = document.createElement('div');
    todayMarker.className = 'today-marker';
    todayMarker.style.left = `${position}%`;

    // Create "Aujourd'hui" label (now above the date)
    const todayLabel = document.createElement('div');
    todayLabel.className = 'today-label';
    todayLabel.style.left = '50%'; // Center with the marker

    // Create text span
    const textSpan = document.createElement('span');
    textSpan.textContent = 'Aujourd\'hui';

    // Add a small arrow pointing down inline with the text
    const arrow = document.createElement('span');
    arrow.innerHTML = '▼'; // Down arrow symbol
    arrow.style.fontSize = '8px';
    arrow.style.color = 'var(--accent-primary)';

    // Add both elements to the label
    todayLabel.appendChild(textSpan);
    todayLabel.appendChild(arrow);

    // Create date label
    const dateLabel = document.createElement('div');
    dateLabel.className = 'today-date';
    dateLabel.textContent = todayFormattedDate;
    dateLabel.style.left = '50%'; // Center with the marker

    // Create vertical line for today marker
    const verticalLine = document.createElement('div');
    verticalLine.className = 'today-vertical-line';
    verticalLine.style.left = '50%'; // Center with the marker

    // Create activity message
    const activityMessage = document.createElement('div');
    activityMessage.className = 'today-activity';

    if (todayActivity) {
      // If there's an activity today, display its details
      activityMessage.innerHTML = `<i class="fas fa-calendar-check"></i> ${todayActivity.activite}`;
    } else {
      // If there's no activity today, display the default message
      activityMessage.innerHTML = `<i class="fas fa-calendar-day"></i> Rien de spécial pour aujourd'hui`;
    }

    // Add elements to timeline
    todayMarker.appendChild(todayLabel);
    todayMarker.appendChild(dateLabel);
    todayMarker.appendChild(verticalLine);
    todayMarker.appendChild(activityMessage);
    timelineLine.appendChild(todayMarker);

    // Return the marker element so we can scroll to it
    return todayMarker;
  }

  // Make the showActivityDetailsModal function available globally
  window.showActivityDetailsModal = function(activities, activeIndex = 0) {
    // Get the modal elements
    const modal = document.getElementById('activity-details-modal');
    const modalTitle = document.getElementById('modal-activity-title');
    const modalNav = document.getElementById('modal-activity-nav');
    const modalActivityName = document.getElementById('modal-activity-name');
    const modalActivityStartDate = document.getElementById('modal-activity-start-date');
    const modalActivityEndDate = document.getElementById('modal-activity-end-date');
    const modalActivityDuration = document.getElementById('modal-activity-duration');
    const modalActivityLocation = document.getElementById('modal-activity-location');
    const modalActivityReference = document.getElementById('modal-activity-reference');
    const modalActivityObservations = document.getElementById('modal-activity-observations');
    const modalActivitySource = document.getElementById('modal-activity-source');
    const closeModal = document.querySelector('.close-modal');

    console.log('Opening activity details modal for', activities);

    // Clear navigation dots
    modalNav.innerHTML = '';

    // Create navigation tabs for multiple activities
    if (activities.length > 1) {
      activities.forEach((activity, index) => {
        const tab = document.createElement('div');
        tab.className = 'activity-nav-tab';
        if (index === activeIndex) {
          tab.classList.add('active');
        }

        // Get activity name and trim if too long
        const activityName = activity.activite || 'Sans nom';
        const trimmedName = activityName.length > 15 ? activityName.substring(0, 12) + '...' : activityName;
        tab.textContent = trimmedName;

        // Add click event to switch between activities
        tab.addEventListener('click', () => {
          // Update active tab
          const tabs = modalNav.querySelectorAll('.activity-nav-tab');
          tabs.forEach(t => t.classList.remove('active'));
          tab.classList.add('active');

          // Update modal content with the selected activity
          updateModalContent(activities[index]);
        });

        modalNav.appendChild(tab);
      });

      // Show navigation tabs
      modalNav.style.display = 'flex';
    } else {
      // Hide navigation for single activity
      modalNav.style.display = 'none';
    }

    // Function to update modal content with activity details
    function updateModalContent(activity) {
      console.log('Updating modal content with activity:', activity);

      // Set modal title based on activity source
      let sourceText = '';
      switch (activity.source) {
        case 'GSA':
          sourceText = 'Activité GSA';
          break;
        case 'DRI':
          sourceText = 'Activité DRI';
          break;
        case 'Mission':
          sourceText = 'Mission GSA';
          break;
        default:
          sourceText = 'Activité';
      }
      modalTitle.textContent = sourceText;

      // Set activity details
      modalActivityName.textContent = activity.activite || 'Non spécifié';

      // Format dates
      const startDate = new Date(activity.du);
      modalActivityStartDate.textContent = formatDateWithDay(startDate);

      if (activity.au) {
        const endDate = new Date(activity.au);
        modalActivityEndDate.textContent = formatDateWithDay(endDate);

        // Calculate and format duration
        const durationInMs = endDate.getTime() - startDate.getTime();
        const durationInDays = Math.ceil(durationInMs / (24 * 60 * 60 * 1000));

        if (durationInDays <= 0) {
          modalActivityDuration.textContent = '1 jour';
        } else if (durationInDays < 7) {
          modalActivityDuration.textContent = `${durationInDays} jour${durationInDays > 1 ? 's' : ''}`;
        } else {
          const weeks = Math.floor(durationInDays / 7);
          const remainingDays = durationInDays % 7;

          if (remainingDays === 0) {
            modalActivityDuration.textContent = `${weeks.toString().padStart(2, '0')} semaine${weeks > 1 ? 's' : ''}`;
          } else {
            modalActivityDuration.textContent = `${weeks.toString().padStart(2, '0')} semaine${weeks > 1 ? 's' : ''} ${remainingDays.toString().padStart(2, '0')} jour${remainingDays > 1 ? 's' : ''}`;
          }
        }
      } else {
        modalActivityEndDate.textContent = 'Non spécifié';
        modalActivityDuration.textContent = 'Non spécifié';
      }

      // Set other details
      modalActivityLocation.textContent = activity.lieu || 'Non spécifié';
      modalActivityReference.textContent = activity.reference || 'Non spécifié';
      modalActivityObservations.textContent = activity.obs || 'Aucune observation';
      modalActivitySource.textContent = activity.source || 'Non spécifié';
    }

    // Initialize modal with the active activity
    updateModalContent(activities[activeIndex]);

    // Show the modal with animation
    modal.style.display = 'block';
    setTimeout(() => {
      modal.classList.add('show');
    }, 10);

    // Close modal when clicking the close button
    closeModal.onclick = () => {
      modal.classList.remove('show');
      setTimeout(() => {
        modal.style.display = 'none';
      }, 300);
    };

    // Close modal when clicking outside the modal content
    modal.onclick = (event) => {
      if (event.target === modal) {
        modal.classList.remove('show');
        setTimeout(() => {
          modal.style.display = 'none';
        }, 300);
      }
    };
  };
});

// Function to initialize full-page training statistics
function initializeFullPageTrainingStats(stats) {
  console.log('Initializing full-page training statistics');

  // Get domain selection container
  const domainContainer = document.querySelector('.domain-selection-container');
  if (!domainContainer) {
    console.error('Domain selection container not found');
    return;
  }

  // Clear existing domain circles except "Tout"
  const toutCircle = domainContainer.querySelector('[data-domain="tout"]');
  domainContainer.innerHTML = '';
  if (toutCircle) {
    domainContainer.appendChild(toutCircle);
  }

  // Add domain circles for each training domain
  if (stats.trainingDomains && stats.trainingDomains.length > 0) {
    stats.trainingDomains.forEach(domain => {
      const domainCircle = document.createElement('div');
      domainCircle.className = 'domain-circle';
      domainCircle.setAttribute('data-domain', domain.toLowerCase());

      const span = document.createElement('span');
      span.textContent = domain;
      domainCircle.appendChild(span);

      // Add click event
      domainCircle.addEventListener('click', () => {
        selectDomain(domain);
      });

      domainContainer.appendChild(domainCircle);
    });
  }

  // Add click event for "Tout" circle
  if (toutCircle) {
    toutCircle.addEventListener('click', () => {
      selectDomain('tout');
    });
  }

  // Initialize with "Tout" selected
  selectDomain('tout');

  // Function to select a domain
  function selectDomain(selectedDomain) {
    console.log('Selecting domain:', selectedDomain);

    // Update active state - fix the bug by using exact domain matching
    document.querySelectorAll('.domain-circle').forEach(circle => {
      circle.classList.remove('active');
    });

    // Find the correct circle by checking both data-domain attribute and text content
    let selectedCircle = null;
    document.querySelectorAll('.domain-circle').forEach(circle => {
      const domainAttr = circle.getAttribute('data-domain');
      const domainText = circle.querySelector('span')?.textContent?.trim();

      if (domainAttr === selectedDomain.toLowerCase() ||
          domainAttr === selectedDomain ||
          domainText === selectedDomain ||
          (selectedDomain === 'tout' && domainAttr === 'tout')) {
        selectedCircle = circle;
      }
    });

    if (selectedCircle) {
      selectedCircle.classList.add('active');
    } else {
      console.warn('Could not find domain circle for:', selectedDomain);
    }

    // Update statistics display
    if (selectedDomain === 'tout') {
      // Fetch statistics for all domains combined
      fetchDomainStats('tout');
    } else {
      // Fetch domain-specific statistics
      fetchDomainStats(selectedDomain);
    }
  }

  // Function to fetch domain-specific statistics
  async function fetchDomainStats(domain) {
    try {
      const response = await window.api.getTrainingStatisticsByDomain(domain);
      if (response && response.success) {
        const displayName = domain === 'tout' ? 'Toutes les formations' : domain;
        updateFullPageStats(response.data, displayName);
      } else {
        console.error('Failed to fetch domain statistics:', response.error);
      }
    } catch (error) {
      console.error('Error fetching domain statistics:', error);
    }
  }

  // Function to update the full-page statistics display
  function updateFullPageStats(trainingData, domainName) {
    console.log('Updating full-page stats for:', domainName, trainingData);

    // Update total count with animation
    const totalCountElement = document.getElementById('training-total-count');
    if (totalCountElement) {
      animateNumber(totalCountElement, 0, trainingData.total || 0, 1000);
    }

    // Update label
    const totalLabelElement = document.getElementById('training-total-label');
    if (totalLabelElement) {
      totalLabelElement.textContent = domainName === 'Toutes les formations' ? 'Formations' : `Formations ${domainName}`;
    }

    // Update summary text
    const summaryTextElement = document.getElementById('training-summary-text');
    if (summaryTextElement) {
      const total = trainingData.total || 0;
      const typeCount = trainingData.byType ? trainingData.byType.length : 0;
      summaryTextElement.textContent = `${total} formations disponibles réparties en ${typeCount} types différents dans ${domainName === 'Toutes les formations' ? 'tous les domaines' : `le domaine ${domainName}`}.`;
    }

    // Update formations list
    updateFormationsList(domainName);

    // Update types breakdown with animation
    const typesBreakdownElement = document.getElementById('training-types-breakdown');
    if (typesBreakdownElement && trainingData.byType) {
      typesBreakdownElement.innerHTML = '';

      trainingData.byType.forEach((type, index) => {
        const typeItem = document.createElement('div');
        typeItem.className = 'training-type-item';
        typeItem.style.opacity = '0';
        typeItem.style.transform = 'translateX(-20px)';
        typeItem.style.transition = 'all 0.5s ease';

        const typeName = document.createElement('div');
        typeName.className = 'training-type-name';
        typeName.textContent = type.type || 'Non spécifié';

        const typeCount = document.createElement('div');
        typeCount.className = 'training-type-count';
        typeCount.textContent = '0';

        typeItem.appendChild(typeName);
        typeItem.appendChild(typeCount);
        typesBreakdownElement.appendChild(typeItem);

        // Animate in with delay
        setTimeout(() => {
          typeItem.style.opacity = '1';
          typeItem.style.transform = 'translateX(0)';
          animateNumber(typeCount, 0, type.count, 800);
        }, index * 100);
      });
    }

    // Update evolution chart and percentage
    updateEvolutionChart(trainingData, domainName);
  }

  // Function to animate numbers
  function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const difference = end - start;

    function updateNumber(currentTime) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const current = Math.round(start + (difference * easeOutQuart));

      element.textContent = current;

      if (progress < 1) {
        requestAnimationFrame(updateNumber);
      }
    }

    requestAnimationFrame(updateNumber);
  }

  // Function to update evolution chart
  function updateEvolutionChart(trainingData, domainName) {
    const chartCanvas = document.getElementById('training-evolution-chart');
    const percentageElement = document.getElementById('evolution-percentage');
    const detailsTextElement = document.getElementById('evolution-details-text');

    if (!chartCanvas) {
      console.error('Evolution chart canvas not found');
      return;
    }

    // Calculate percentage change and detailed analysis
    let percentChange = 0;
    let analysisText = 'Données insuffisantes pour l\'analyse de tendance.';
    let currentYear = new Date().getFullYear();
    let totalGrowth = 0;

    if (trainingData.yearlyData && trainingData.yearlyData.byDomain) {
      // For "Tout", use "tout" as the key, otherwise use the domain name
      const domainKey = domainName === 'Toutes les formations' ? 'tout' : domainName;
      const domainData = trainingData.yearlyData.byDomain[domainKey];

      if (domainData && Array.isArray(domainData) && domainData.length >= 2) {
        const current = domainData[domainData.length - 1];
        const previous = domainData[domainData.length - 2];
        const first = domainData[0];

        if (previous.count > 0) {
          percentChange = ((current.count - previous.count) / previous.count * 100);
        }

        if (first.count > 0 && domainData.length > 2) {
          totalGrowth = ((current.count - first.count) / first.count * 100);
        }

        // Generate detailed analysis
        const trend = percentChange > 5 ? 'forte croissance' :
                     percentChange > 0 ? 'croissance modérée' :
                     percentChange > -5 ? 'stabilité' : 'décroissance';

        analysisText = `Tendance ${trend} avec ${Math.abs(percentChange).toFixed(1)}% ${percentChange >= 0 ? 'de croissance' : 'de baisse'} par rapport à ${previous.year}. `;

        if (domainData.length > 2) {
          analysisText += `Croissance globale de ${totalGrowth.toFixed(1)}% depuis ${first.year}.`;
        }
      }
    }

    // Update percentage display with animation
    if (percentageElement) {
      // Animate percentage
      animatePercentage(percentageElement, 0, percentChange, 1200);

      // Add color class
      percentageElement.className = 'evolution-percentage';
      if (percentChange > 0) {
        percentageElement.style.color = '#4CAF50';
      } else if (percentChange < 0) {
        percentageElement.style.color = '#F44336';
      } else {
        percentageElement.style.color = 'var(--accent-primary)';
      }
    }

    // Update analysis text
    if (detailsTextElement) {
      detailsTextElement.textContent = analysisText;
    }

    // Create or update chart
    if (window.evolutionChart) {
      window.evolutionChart.destroy();
    }

    const ctx = chartCanvas.getContext('2d');

    // Prepare chart data
    let labels = [];
    let data = [];

    if (trainingData.yearlyData && trainingData.yearlyData.allYears) {
      labels = trainingData.yearlyData.allYears.map(year => year.toString());

      // For "Tout", use "tout" as the key, otherwise use the domain name
      const domainKey = domainName === 'Toutes les formations' ? 'tout' : domainName;
      const domainData = trainingData.yearlyData.byDomain[domainKey];

      if (domainData && Array.isArray(domainData)) {
        data = trainingData.yearlyData.allYears.map(year => {
          const yearData = domainData.find(d => d.year === year);
          return yearData ? yearData.count : 0;
        });
      }
    }

    // Create gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, 'rgba(30, 144, 255, 0.3)');
    gradient.addColorStop(0.5, 'rgba(30, 144, 255, 0.1)');
    gradient.addColorStop(1, 'rgba(30, 144, 255, 0.05)');

    // Create chart with modern styling and animations
    window.evolutionChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'Formations',
          data: data,
          borderColor: '#1E90FF',
          backgroundColor: gradient,
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#1E90FF',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 3,
          pointRadius: 6,
          pointHoverRadius: 10,
          pointHoverBackgroundColor: '#4169E1',
          pointHoverBorderColor: '#ffffff',
          pointHoverBorderWidth: 4,
          shadowColor: 'rgba(30, 144, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowOffsetY: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        animation: {
          duration: 2000,
          easing: 'easeOutQuart',
          delay: (context) => {
            return context.type === 'data' && context.mode === 'default' ? context.dataIndex * 100 : 0;
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: '#1E90FF',
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: false,
            callbacks: {
              title: function(context) {
                return `Année ${context[0].label}`;
              },
              label: function(context) {
                return `${context.parsed.y} formations`;
              }
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(255, 255, 255, 0.08)',
              drawBorder: false
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)',
              font: {
                size: 12,
                weight: '500'
              },
              padding: 10
            }
          },
          x: {
            grid: {
              color: 'rgba(255, 255, 255, 0.08)',
              drawBorder: false
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.7)',
              font: {
                size: 12,
                weight: '500'
              },
              padding: 10
            }
          }
        },
        elements: {
          line: {
            tension: 0.4
          }
        }
      }
    });
  }

  // Function to animate percentage
  function animatePercentage(element, start, end, duration) {
    const startTime = performance.now();
    const difference = end - start;

    function updatePercentage(currentTime) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const current = start + (difference * easeOutQuart);

      const sign = current > 0 ? '+' : '';
      element.textContent = `${sign}${current.toFixed(1)}%`;

      if (progress < 1) {
        requestAnimationFrame(updatePercentage);
      }
    }

    requestAnimationFrame(updatePercentage);
  }

  // Function to update formations list
  async function updateFormationsList(domainName) {
    const formationsListElement = document.getElementById('formations-list');
    if (!formationsListElement) return;

    try {
      // Show loading state
      formationsListElement.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--text-secondary);">Chargement...</div>';

      // Fetch formations data
      const domain = domainName === 'Toutes les formations' ? 'tout' : domainName;
      const response = await window.api.getFormationsByDomain(domain);

      if (response && response.success && response.data) {
        const formations = response.data;

        if (formations.length === 0) {
          formationsListElement.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--text-secondary);">Aucune formation trouvée</div>';
          return;
        }

        // Clear the list
        formationsListElement.innerHTML = '';

        // Add each formation
        formations.forEach((formation, index) => {
          const formationItem = document.createElement('div');
          formationItem.className = 'formation-item';
          formationItem.style.opacity = '0';
          formationItem.style.transform = 'translateY(10px)';
          formationItem.style.transition = 'all 0.3s ease';

          const formationName = document.createElement('div');
          formationName.className = 'formation-name';
          formationName.textContent = formation.nom || 'Formation sans nom';

          const formationTime = document.createElement('div');
          formationTime.className = 'formation-time';

          // Format the instance count and time information with bullet point
          const instanceCount = formation.instanceCount || 0;
          const instanceText = instanceCount === 1 ? 'instance' : 'instances';
          const timeText = formatTimeAgo(formation.lastInstanceDate);

          formationTime.innerHTML = `${instanceCount} ${instanceText} • ${timeText}`;

          formationItem.appendChild(formationName);
          formationItem.appendChild(formationTime);
          formationsListElement.appendChild(formationItem);

          // Animate in with delay
          setTimeout(() => {
            formationItem.style.opacity = '1';
            formationItem.style.transform = 'translateY(0)';
          }, index * 50);
        });
      } else {
        formationsListElement.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--text-secondary);">Erreur lors du chargement</div>';
      }
    } catch (error) {
      console.error('Error updating formations list:', error);
      formationsListElement.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--text-secondary);">Erreur lors du chargement</div>';
    }
  }

  // Function to format time ago
  function formatTimeAgo(dateString) {
    if (!dateString) return 'Date inconnue';

    const date = new Date(dateString);
    const now = new Date();
    const diffTime = now - date;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const diffWeeks = Math.floor(diffDays / 7);
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);

    if (diffTime < 0) {
      // Future date
      const absDiffDays = Math.abs(diffDays);
      const absDiffWeeks = Math.floor(absDiffDays / 7);
      const absDiffMonths = Math.floor(absDiffDays / 30);
      const absDiffYears = Math.floor(absDiffDays / 365);

      if (absDiffYears > 0) {
        return `Dans ${absDiffYears} an${absDiffYears > 1 ? 's' : ''}`;
      } else if (absDiffMonths > 0) {
        return `Dans ${absDiffMonths} mois`;
      } else if (absDiffWeeks > 0) {
        return `Dans ${absDiffWeeks} semaine${absDiffWeeks > 1 ? 's' : ''}`;
      } else if (absDiffDays > 0) {
        return `Dans ${absDiffDays} jour${absDiffDays > 1 ? 's' : ''}`;
      } else {
        return 'Aujourd\'hui';
      }
    } else {
      // Past date
      if (diffYears > 0) {
        return `Il y a ${diffYears} an${diffYears > 1 ? 's' : ''}`;
      } else if (diffMonths > 0) {
        return `Il y a ${diffMonths} mois`;
      } else if (diffWeeks > 0) {
        return `Il y a ${diffWeeks} semaine${diffWeeks > 1 ? 's' : ''}`;
      } else if (diffDays > 0) {
        return `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
      } else {
        return 'Aujourd\'hui';
      }
    }
  }
}

// Function to load and display statistics
async function loadStatistics() {
  console.log('loadStatistics function called');
  const kpiContainer = document.querySelector('.kpi-container');
  if (!kpiContainer) {
    console.error('KPI container element not found');
    return Promise.reject(new Error('KPI container element not found'));
  }

  const loadingMessage = kpiContainer.querySelector('.loading-message');
  const kpiRow = kpiContainer.querySelector('.kpi-row');
  const errorMessage = kpiContainer.querySelector('.error-message');

  if (!loadingMessage || !kpiRow || !errorMessage) {
    console.error('Required elements not found in KPI container:', {
      loadingMessage: !!loadingMessage,
      kpiRow: !!kpiRow,
      errorMessage: !!errorMessage
    });
    return Promise.reject(new Error('Required elements not found in KPI container'));
  }

  // Show loading message
  loadingMessage.style.display = 'block';
  kpiRow.style.display = 'none';
  errorMessage.style.display = 'none';

  try {
    console.log('Calling window.api.getStatistics()');
    // Fetch statistics from the database
    if (!window.api || !window.api.getStatistics) {
      console.error('window.api.getStatistics is not available:', window.api);
      throw new Error('API method not available');
    }

    const response = await window.api.getStatistics();
    console.log('Response from getStatistics:', response);

    if (response && response.success) {
      const stats = response.data;
      console.log('Statistics loaded:', stats);

      // Initialize the new full-page training statistics
      initializeFullPageTrainingStats(stats);

      // Show the KPI row
      const kpiRow = document.querySelector('.kpi-row');
      if (kpiRow) {
        kpiRow.style.display = 'flex';
      }

      // Hide loading message
      const loadingMessage = document.querySelector('.loading-message');
      if (loadingMessage) {
        loadingMessage.style.display = 'none';
      }

      // Return the stats for promise chaining
      return stats;
    } else {
      console.error('Failed response from getStatistics:', response);
      throw new Error((response && response.error) || 'Failed to load statistics');
    }
  } catch (error) {
    console.error('Error loading statistics:', error);

    // Show error message
    const loadingMessage = document.querySelector('.loading-message');
    const errorMessage = document.querySelector('.error-message');

    if (loadingMessage) loadingMessage.style.display = 'none';
    if (errorMessage) {
      errorMessage.style.display = 'block';
      errorMessage.textContent = `Erreur lors du chargement des statistiques: ${error.message}`;
    }

    // Rethrow the error for promise chaining
    throw error;
  }
}

// Load statistics when the dashboard tab is shown
document.querySelector('.tab[data-tab="dashboard"]').addEventListener('click', () => {
  loadStatistics();
});

// Load statistics on initial page load
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOMContentLoaded event fired');

  // Initialize the first tab (dashboard) by default
  switchTab('dashboard');

  // Load statistics for the dashboard
  console.log('Calling loadStatistics from DOMContentLoaded');
  loadStatistics();
});

// Call loadStatistics directly to ensure it's called
console.log('Calling loadStatistics directly');
setTimeout(() => {
  loadStatistics();
}, 2000);

// Global animation functions for both KPI views
function animateNumber(element, start, end, duration) {
  const startTime = performance.now();
  const difference = end - start;

  function updateNumber(currentTime) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // Easing function for smooth animation
    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
    const current = Math.round(start + (difference * easeOutQuart));

    element.textContent = current;

    if (progress < 1) {
      requestAnimationFrame(updateNumber);
    }
  }

  requestAnimationFrame(updateNumber);
}

function animatePercentage(element, start, end, duration) {
  const startTime = performance.now();
  const difference = end - start;

  function updatePercentage(currentTime) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // Easing function for smooth animation
    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
    const current = start + (difference * easeOutQuart);

    const sign = current > 0 ? '+' : '';
    element.textContent = `${sign}${current.toFixed(1)}%`;

    if (progress < 1) {
      requestAnimationFrame(updatePercentage);
    }
  }

  requestAnimationFrame(updatePercentage);
}

// Initialize formations statistics - Global function
window.initializeFormationsStatistics = async function() {
  console.log('Initializing formations statistics...');

  try {
    // Fetch statistics first
    const response = await window.api.getStatistics();
    if (response && response.success) {
      // Initialize the formations KPI view with statistics
      initializeFullPageTrainingStats(response.data);
    } else {
      console.error('Failed to fetch statistics for formations KPI:', response.error);
    }
  } catch (error) {
    console.error('Error initializing formations statistics:', error);
  }
};

// Initialize trainee statistics - Global function
window.initializeTraineeStatistics = async function() {
  console.log('Initializing trainee statistics...');

  const kpiContainer = document.querySelector('#dashboard-trainee-kpi-view .kpi-container');
  if (!kpiContainer) {
    console.error('Trainee KPI container element not found');
    return;
  }

  const loadingMessage = kpiContainer.querySelector('.loading-message');
  const kpiRow = kpiContainer.querySelector('.kpi-row');
  const errorMessage = kpiContainer.querySelector('.error-message');

  if (!loadingMessage || !kpiRow) {
    console.error('Required elements not found in trainee KPI container');
    return;
  }

  // Show loading message
  loadingMessage.style.display = 'block';
  kpiRow.style.display = 'none';
  if (errorMessage) errorMessage.style.display = 'none';

  try {
    // Fetch statistics from the database
    const response = await window.api.getStatistics();
    console.log('Response from getStatistics for trainees:', response);

    if (response && response.success) {
      const stats = response.data;
      console.log('Statistics loaded for trainees:', stats);

      // Initialize the trainee KPI view
      initializeTraineeKPI(stats);

      // Show the KPI row
      kpiRow.style.display = 'flex';
      loadingMessage.style.display = 'none';
    } else {
      throw new Error((response && response.error) || 'Failed to load statistics');
    }
  } catch (error) {
    console.error('Error initializing trainee statistics:', error);

    // Show error message
    loadingMessage.style.display = 'none';
    if (errorMessage) {
      errorMessage.style.display = 'block';
      errorMessage.textContent = `Erreur lors du chargement des statistiques: ${error.message}`;
    }
  }
};

// Initialize trainee KPI view
function initializeTraineeKPI(stats) {
  console.log('Initializing trainee KPI view');

  // Get domain selection container
  const domainContainer = document.querySelector('#trainee-domain-selection-container');
  if (!domainContainer) {
    console.error('Trainee domain selection container not found');
    return;
  }

  // Clear existing domain circles except "Tout"
  const toutCircle = domainContainer.querySelector('[data-domain="tout"]');
  domainContainer.innerHTML = '';
  if (toutCircle) {
    domainContainer.appendChild(toutCircle);
  }

  // Add domain circles for each training domain
  if (stats.trainingDomains && stats.trainingDomains.length > 0) {
    stats.trainingDomains.forEach(domain => {
      const domainCircle = document.createElement('div');
      domainCircle.className = 'domain-circle';
      domainCircle.setAttribute('data-domain', domain.toLowerCase());

      const span = document.createElement('span');
      span.textContent = domain;
      domainCircle.appendChild(span);

      // Add click event
      domainCircle.addEventListener('click', () => {
        selectTraineeDomain(domain);
      });

      domainContainer.appendChild(domainCircle);
    });
  }

  // Add click event for "Tout" circle
  if (toutCircle) {
    toutCircle.addEventListener('click', () => {
      selectTraineeDomain('tout');
    });
  }

  // Initialize with "Tout" selected
  selectTraineeDomain('tout');
}

// Select trainee domain
function selectTraineeDomain(selectedDomain) {
  console.log('Selecting trainee domain:', selectedDomain);

  // Update active state of domain circles
  document.querySelectorAll('#trainee-domain-selection-container .domain-circle').forEach(circle => {
    circle.classList.remove('active');
  });

  // Find the correct circle
  let selectedCircle = null;
  document.querySelectorAll('#trainee-domain-selection-container .domain-circle').forEach(circle => {
    const domainAttr = circle.getAttribute('data-domain');
    const domainText = circle.querySelector('span')?.textContent?.trim();

    if (domainAttr === selectedDomain.toLowerCase() ||
        domainAttr === selectedDomain ||
        domainText === selectedDomain ||
        (selectedDomain === 'tout' && domainAttr === 'tout')) {
      selectedCircle = circle;
    }
  });

  if (selectedCircle) {
    selectedCircle.classList.add('active');
  }

  // Fetch and update trainee statistics
  fetchTraineeStats(selectedDomain);
}

// Fetch trainee statistics for domain
async function fetchTraineeStats(domain) {
  try {
    const response = await window.api.getTraineeStatisticsByDomain(domain);
    if (response && response.success) {
      const displayName = domain === 'tout' ? 'Tous les stagiaires' : domain;
      updateTraineeStats(response.data, displayName);
    } else {
      console.error('Failed to fetch trainee statistics:', response.error);
    }
  } catch (error) {
    console.error('Error fetching trainee statistics:', error);
  }
}

// Update trainee statistics display
function updateTraineeStats(traineeData, domainName) {
  console.log('Updating trainee stats for:', domainName, traineeData);

  // Update total count
  const totalCountElement = document.getElementById('trainee-total-count');
  if (totalCountElement) {
    animateNumber(totalCountElement, 0, traineeData.total || 0, 1000);
  }

  // Update label
  const totalLabelElement = document.getElementById('trainee-total-label');
  if (totalLabelElement) {
    totalLabelElement.textContent = domainName === 'Tous les stagiaires' ? 'Stagiaires' : `Stagiaires ${domainName}`;
  }

  // Update types list
  const typesListElement = document.getElementById('trainee-types-list');
  if (typesListElement && traineeData.byType) {
    typesListElement.innerHTML = '';

    traineeData.byType.forEach((type, index) => {
      const typeItem = document.createElement('div');
      typeItem.className = 'training-type-item';
      typeItem.style.opacity = '0';
      typeItem.style.transform = 'translateX(-20px)';

      const typeName = document.createElement('div');
      typeName.className = 'training-type-name';
      typeName.textContent = type.type || 'Non spécifié';

      const typeCount = document.createElement('div');
      typeCount.className = 'training-type-count';
      typeCount.textContent = '0';

      typeItem.appendChild(typeName);
      typeItem.appendChild(typeCount);
      typesListElement.appendChild(typeItem);

      // Animate in with delay
      setTimeout(() => {
        typeItem.style.opacity = '1';
        typeItem.style.transform = 'translateX(0)';
        animateNumber(typeCount, 0, type.count, 800);
      }, index * 100);
    });
  }

  // Update top unités list
  const topUnitesListElement = document.getElementById('top-unites-list');
  if (topUnitesListElement && traineeData.byUnite) {
    topUnitesListElement.innerHTML = '';

    traineeData.byUnite.forEach((unite, index) => {
      const uniteItem = document.createElement('div');
      uniteItem.className = 'unite-item';
      uniteItem.style.opacity = '0';
      uniteItem.style.transform = 'translateY(-10px)';

      const uniteName = document.createElement('div');
      uniteName.className = 'unite-name';
      uniteName.textContent = unite.unite || 'Unité non spécifiée';

      const uniteCount = document.createElement('div');
      uniteCount.className = 'unite-count';
      uniteCount.textContent = '0';

      uniteItem.appendChild(uniteName);
      uniteItem.appendChild(uniteCount);
      topUnitesListElement.appendChild(uniteItem);

      // Animate in with delay
      setTimeout(() => {
        uniteItem.style.opacity = '1';
        uniteItem.style.transform = 'translateY(0)';
        animateNumber(uniteCount, 0, unite.count, 800);
      }, index * 150);
    });
  }

  // Update evolution chart
  updateTraineeEvolutionChart(traineeData, domainName);
}

// Update trainee evolution chart
function updateTraineeEvolutionChart(traineeData, domainName) {
  console.log('Updating trainee evolution chart for:', domainName);
  console.log('Trainee data received:', traineeData);

  const chartCanvas = document.getElementById('trainee-evolution-chart');
  const percentageElement = document.getElementById('trainee-evolution-percentage');
  const detailsTextElement = document.getElementById('trainee-evolution-details-text');

  if (!chartCanvas) {
    console.error('Trainee evolution chart canvas not found');
    return;
  }

  // Calculate percentage change
  let percentChange = 0;
  let analysisText = 'Données insuffisantes pour l\'analyse de tendance.';

  if (traineeData.yearlyData && traineeData.yearlyData.byDomain) {
    const domainKey = domainName === 'Tous les stagiaires' ? 'tout' : domainName;
    const domainData = traineeData.yearlyData.byDomain[domainKey];

    if (domainData && Array.isArray(domainData) && domainData.length >= 2) {
      const current = domainData[domainData.length - 1];
      const previous = domainData[domainData.length - 2];

      if (previous.count > 0) {
        percentChange = ((current.count - previous.count) / previous.count * 100);
      }

      const trend = percentChange > 5 ? 'forte croissance' :
                   percentChange > 0 ? 'croissance modérée' :
                   percentChange > -5 ? 'stabilité' : 'décroissance';

      analysisText = `Tendance ${trend} avec ${Math.abs(percentChange).toFixed(1)}% ${percentChange >= 0 ? 'de croissance' : 'de baisse'} par rapport à ${previous.year}.`;
    }
  }

  // Update percentage display
  if (percentageElement) {
    animatePercentage(percentageElement, 0, percentChange, 1200);
    percentageElement.style.color = percentChange > 0 ? '#4CAF50' : percentChange < 0 ? '#F44336' : 'var(--accent-primary)';
  }

  // Update analysis text
  if (detailsTextElement) {
    detailsTextElement.textContent = analysisText;
  }

  // Create or update chart
  if (window.traineeEvolutionChart) {
    window.traineeEvolutionChart.destroy();
  }

  const ctx = chartCanvas.getContext('2d');

  // Prepare chart data
  let labels = [];
  let data = [];

  if (traineeData.yearlyData && traineeData.yearlyData.allYears) {
    labels = traineeData.yearlyData.allYears.map(year => year.toString());
    console.log('Chart labels (years):', labels);

    const domainKey = domainName === 'Tous les stagiaires' ? 'tout' : domainName;
    const domainData = traineeData.yearlyData.byDomain[domainKey];
    console.log('Domain key:', domainKey);
    console.log('Domain data:', domainData);

    if (domainData && Array.isArray(domainData)) {
      data = traineeData.yearlyData.allYears.map(year => {
        const yearData = domainData.find(d => d.year === year);
        return yearData ? yearData.count : 0;
      });
      console.log('Chart data (trainee counts):', data);
    }
  } else {
    console.log('No yearly data available for trainee chart');
  }

  console.log('Final chart labels:', labels);
  console.log('Final chart data:', data);

  // Create gradient
  const gradient = ctx.createLinearGradient(0, 0, 0, 400);
  gradient.addColorStop(0, 'rgba(30, 144, 255, 0.3)');
  gradient.addColorStop(1, 'rgba(30, 144, 255, 0.05)');

  // Create chart
  window.traineeEvolutionChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'Stagiaires',
        data: data,
        borderColor: '#1E90FF',
        backgroundColor: gradient,
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#1E90FF',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 3,
        pointRadius: 6,
        pointHoverRadius: 10,
        pointHoverBackgroundColor: '#4169E1',
        pointHoverBorderColor: '#ffffff',
        pointHoverBorderWidth: 4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        x: {
          grid: {
            color: 'rgba(255, 255, 255, 0.1)',
            borderColor: 'rgba(255, 255, 255, 0.2)'
          },
          ticks: {
            color: 'var(--text-secondary)',
            font: {
              size: 11
            }
          }
        },
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(255, 255, 255, 0.1)',
            borderColor: 'rgba(255, 255, 255, 0.2)'
          },
          ticks: {
            color: 'var(--text-secondary)',
            font: {
              size: 11
            }
          }
        }
      },
      animation: {
        duration: 2000,
        easing: 'easeInOutQuart'
      }
    }
  });
}
