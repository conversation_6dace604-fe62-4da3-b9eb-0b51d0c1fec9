-- <PERSON><PERSON> script to add the 'reference' field to the three activity tables

-- Add 'reference' field to activites_gsa table if it doesn't exist
ALTER TABLE `activites_gsa` 
ADD COLUMN IF NOT EXISTS `reference` VARCHAR(100) NULL DEFAULT NULL COMMENT 'Reference number for the activity';

-- Add 'reference' field to activites_dri table if it doesn't exist
ALTER TABLE `activites_dri` 
ADD COLUMN IF NOT EXISTS `reference` VARCHAR(100) NULL DEFAULT NULL COMMENT 'Reference number for the activity';

-- Add 'reference' field to missions_gsa table if it doesn't exist
ALTER TABLE `missions_gsa` 
ADD COLUMN IF NOT EXISTS `reference` VARCHAR(100) NULL DEFAULT NULL COMMENT 'Reference number for the activity';

-- Update some existing records with sample reference values for testing
-- activites_gsa
UPDATE `activites_gsa` SET `reference` = CONCAT('GSA-', id, '-', YEAR(du)) WHERE `reference` IS NULL LIMIT 10;

-- activites_dri
UPDATE `activites_dri` SET `reference` = CONCAT('DRI-', id, '-', YEAR(du)) WHERE `reference` IS NULL LIMIT 10;

-- missions_gsa
UPDATE `missions_gsa` SET `reference` = CONCAT('MSN-', id, '-', YEAR(du)) WHERE `reference` IS NULL LIMIT 10;

-- Verify the changes
SELECT 'activites_gsa' AS table_name, id, activité, reference FROM activites_gsa LIMIT 5;
SELECT 'activites_dri' AS table_name, id, activité, reference FROM activites_dri LIMIT 5;
SELECT 'missions_gsa' AS table_name, id, activité, reference FROM missions_gsa LIMIT 5;
