<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' https://unpkg.com; script-src 'self' 'unsafe-inline' https://unpkg.com; style-src 'self' 'unsafe-inline' https://unpkg.com; font-src 'self'; img-src 'self' data: https://*.tile.openstreetmap.org"
    />
    <title>SMCA GSA Dashboard</title>
    <link rel="stylesheet" href="styles.css" />
    <link rel="stylesheet" href="loading-indicator.css" />
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="assets/leaflet/leaflet.css" />
    <!-- Font Awesome for icons (local) -->
    <link rel="stylesheet" href="assets/fontawesome/css/font-face.css" />
    <link rel="stylesheet" href="assets/fontawesome/css/all.min.css" />
  </head>
  <body>
    <!-- Loading Indicator Template (will be cloned by JS) -->
    <template id="loading-indicator-template">
      <div class="loading-container">
        <div class="loading-text">
          <span class="loading-letter">G</span>
          <span class="loading-letter">.</span>
          <span class="loading-letter">S</span>
          <span class="loading-letter">.</span>
          <span class="loading-letter">A</span>
        </div>
      </div>
    </template>

    <!-- Database Connection Error Screen -->
    <div class="db-error-screen" id="db-error-screen">
      <div class="db-error-content">
        <div class="db-error-icon">
          <i class="fas fa-database"></i>
          <i class="fas fa-times db-error-x"></i>
        </div>
        <h2>Veuillez vérifier la connexion à la base de données</h2>
        <p id="db-error-message">
          Impossible de se connecter au serveur de base de données.
        </p>
        <button id="retry-connection" class="action-btn">Réessayer</button>
      </div>
    </div>

    <div class="app-container" id="app-container">
      <!-- Floating lateral tabs -->
      <div class="lateral-tabs" id="lateral-tabs">
        <div class="tab active" data-tab="dashboard">
          <i class="fas fa-tachometer-alt"></i>
          <span class="tab-tooltip">Dashboard</span>
        </div>
        <div class="tab" data-tab="map">
          <i class="fas fa-map-marked-alt"></i>
          <span class="tab-tooltip">Map</span>
        </div>
        <div class="tab" data-tab="list">
          <i class="fas fa-list-ul"></i>
          <span class="tab-tooltip">List</span>
        </div>
        <div class="tab" data-tab="actions">
          <i class="fas fa-tasks"></i>
          <span class="tab-tooltip">Actions</span>
        </div>
        <!-- Toggle button for collapsing/expanding tabs -->
        <div class="tabs-toggle" id="tabs-toggle">
          <i class="fas fa-chevron-left"></i>
        </div>
      </div>

      <!-- Content area -->
      <div class="content-area">
        <!-- Dashboard Tab Content -->
        <div class="tab-content active" id="dashboard-content">
          <h1>Dashboard</h1>

          <!-- Dashboard Tabs -->
          <div class="dashboard-tabs-container">
            <div class="dashboard-tabs">
              <button class="dashboard-tab active" data-tab="activites">Activités</button>
              <button class="dashboard-tab" data-tab="kpis">KPIs</button>
              <button class="dashboard-tab" data-tab="missions">Missions</button>
              <button class="dashboard-tab" data-tab="effectif">Effectif</button>
              <button class="dashboard-tab" data-tab="dto">DTO</button>
            </div>
          </div>

          <!-- Dashboard Tab Contents -->

          <!-- Activités Tab Content -->
          <div class="dashboard-tab-content active" id="activites-content">
            <!-- Planning Schedule - Horizontal Scrollable Calendar -->
            <div class="dashboard-main-view" id="dashboard-main-view">
            <!-- Planning Schedule - Horizontal Scrollable Calendar -->
          <div class="planning-schedule-container">
            <!-- Control buttons container -->
            <div class="timeline-controls">
              <!-- Left section: Current Year Display -->
              <div class="timeline-left">
                <div id="current-year-display" class="current-year-display">2025</div>
              </div>

              <!-- Center section: Drag indicator -->
              <div class="timeline-center">
                <div class="drag-indicator">
                  <i class="fas fa-arrow-left"></i>
                  <i class="fas fa-hand-pointer"></i>
                  <i class="fas fa-arrow-right"></i>
                </div>
              </div>

              <!-- Right section: Buttons -->
              <div class="timeline-right">
                <button id="refresh-btn" class="timeline-btn refresh-btn" title="Rafraîchir les données">
                  <i class="fas fa-sync-alt"></i>
                </button>
                <button id="current-week-btn" class="timeline-btn current-week-btn" title="Aller à la semaine actuelle">
                  <i class="fas fa-calendar-day"></i>
                </button>
              </div>
            </div>

            <div class="planning-schedule" id="planning-schedule">
              <!-- Week columns will be generated by JavaScript -->
            </div>
            <!-- Fixed training count display -->
            <div id="active-trainings-count" class="active-trainings-count">00 formations en cours</div>
          </div>

          <!-- Activities Timeline - Horizontal Timeline -->
          <div class="activities-timeline-container">
            <div class="activities-timeline" id="activities-timeline">
              <!-- Activities will be generated by JavaScript -->
            </div>
          </div>

          </div>



          <!-- KPIs Tab Content -->
          <div class="dashboard-tab-content" id="kpis-content">
            <!-- Dashboard KPI view - full page statistics -->
            <div class="dashboard-kpi-view" id="dashboard-kpi-view">
              <!-- KPI container with adaptive layout -->
              <div class="kpi-container">
                <!-- First row - Activities KPIs -->
                <div class="kpi-row">
                  <!-- Activities KPI with comparison toggle -->
                  <div class="kpi-item kpi-activities">
                    <!-- Toggle switch for week/month comparison -->
                    <div class="comparison-toggle">
                      <button class="comparison-toggle-btn active" data-period="week">Semaine</button>
                      <button class="comparison-toggle-btn" data-period="month">Mois</button>
                      <div class="toggle-slider"></div>
                    </div>

                    <div class="kpi-icon"><i class="fas fa-calendar-alt"></i></div>
                    <div class="kpi-content">
                      <div class="kpi-value" id="activities-count">0</div>
                      <div class="kpi-label">Activités</div>
                      <div class="kpi-chart-container">
                        <canvas id="activities-chart"></canvas>
                      </div>
                      <div class="kpi-percentage" id="activities-percentage">0%</div>
                      <div class="kpi-comparison-label" id="activities-comparison-label">vs semaine précédente</div>
                    </div>
                  </div>

                  <!-- Total Trainings KPI -->
                  <div class="kpi-item kpi-total-trainings">
                    <div class="kpi-icon"><i class="fas fa-graduation-cap"></i></div>
                    <div class="kpi-content">
                      <!-- Domain toggle buttons -->
                      <div class="domain-toggle-container outside-card">
                        <div class="domain-toggle">
                          <button class="domain-toggle-btn active" data-domain="tout"></button>
                          <!-- Domain buttons will be populated dynamically -->
                          <div class="toggle-slider"></div>
                        </div>
                      </div>

                      <div class="kpi-value" id="total-trainings-count">0</div>
                      <div class="kpi-label">Formations</div>

                      <!-- Training chart container -->
                      <div class="training-chart-container">
                        <canvas id="training-trend-chart"></canvas>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Second row - Full page training statistics (hidden by default) -->
                <div class="kpi-row" style="display: none;">
                  <!-- Full page training statistics with domain selection -->
                  <div class="kpi-training-fullpage">
                    <!-- Domain selection circles - larger and including "Tout" -->
                    <div class="domain-selection-container">
                      <div class="domain-circle active" data-domain="tout">
                        <span>Tout</span>
                      </div>
                      <!-- Domain circles will be populated dynamically -->
                    </div>

                    <!-- Training statistics display -->
                    <div class="training-stats-container">
                      <div class="training-stats-left">
                        <!-- Evolution chart -->
                        <div class="evolution-section">
                          <div class="evolution-title">Évolution des formations</div>
                          <div class="evolution-chart-container">
                            <canvas id="training-evolution-chart"></canvas>
                          </div>
                          <div class="evolution-percentage" id="evolution-percentage">0%</div>
                          <div class="evolution-label">Évolution annuelle</div>

                          <!-- Evolution details -->
                          <div class="evolution-details">
                            <div class="evolution-details-title">Analyse de tendance</div>
                            <div class="evolution-details-text" id="evolution-details-text">
                              Cette métrique montre l'évolution du nombre de formations par rapport à l'année précédente.
                              Une tendance positive indique une augmentation de l'activité de formation.
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="training-stats-right">
                        <!-- Total count and breakdown -->
                        <div class="training-count-section">
                          <!-- Top section with count and formations side by side -->
                          <div class="training-count-header">
                            <div class="training-count-left">
                              <div class="training-total-count" id="training-total-count">0</div>
                              <div class="training-total-label" id="training-total-label">Formations</div>
                            </div>

                            <div class="training-count-right">
                              <div class="formations-list-title">Liste des formations</div>
                              <div class="formations-list" id="formations-list">
                                <!-- Will be populated dynamically -->
                              </div>
                            </div>
                          </div>

                          <!-- Training summary -->
                          <div class="training-summary">
                            <div class="training-summary-title">Résumé des formations</div>
                            <div class="training-summary-text" id="training-summary-text">
                              Nombre total de formations disponibles dans le domaine sélectionné.
                            </div>
                          </div>

                          <!-- Training types breakdown -->
                          <div class="training-types-breakdown" id="training-types-breakdown">
                            <!-- Will be populated dynamically -->
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="error-message" style="display: none;">Erreur lors du chargement des statistiques</div>
              </div>
            </div>
          </div>

          <!-- Missions Tab Content -->
          <div class="dashboard-tab-content" id="missions-content">
            <div class="tab-placeholder">
              <div class="placeholder-icon"><i class="fas fa-tasks"></i></div>
              <h3>Missions</h3>
              <p>Contenu des missions à venir...</p>
            </div>
          </div>

          <!-- Effectif Tab Content -->
          <div class="dashboard-tab-content" id="effectif-content">
            <div class="tab-placeholder">
              <div class="placeholder-icon"><i class="fas fa-users"></i></div>
              <h3>Effectif</h3>
              <p>Gestion des effectifs à venir...</p>
            </div>
          </div>

          <!-- DTO Tab Content -->
          <div class="dashboard-tab-content" id="dto-content">
            <div class="tab-placeholder">
              <div class="placeholder-icon"><i class="fas fa-clipboard-list"></i></div>
              <h3>DTO</h3>
              <p>Données DTO à venir...</p>
            </div>
          </div>
        </div>

        <!-- Map Tab Content -->
        <div class="tab-content" id="map-content">
          <h1>Map View</h1>
          <div class="map-container">
            <div id="morocco-map"></div>

            <!-- GSA Logo Overlay (shown when no toggle is active) -->
            <div class="map-logo-overlay" id="map-logo-overlay">
              <img src="gsa.png" alt="GSA Logo">
            </div>

            <!-- Floating toggle buttons for map data layers -->
            <div class="map-toggle-container">
              <button class="map-toggle-btn" data-layer="missiles">
                <i class="fas fa-rocket"></i>
                <span>Missiles</span>
              </button>
              <button class="map-toggle-btn" data-layer="drones">
                <i class="fas fa-plane"></i>
                <span>Drones</span>
              </button>
              <button class="map-toggle-btn" data-layer="personnel">
                <i class="fas fa-user-shield"></i>
                <span>Personnel drone</span>
              </button>
              <button class="map-toggle-btn" data-layer="stats">
                <i class="fas fa-chart-bar"></i>
                <span>Statistiques</span>
              </button>
            </div>
          </div>
        </div>

        <!-- List Tab Content -->
        <div class="tab-content" id="list-content">
          <h1>Formations</h1>
          <div class="split-container">
            <!-- Panneau de gauche (liste des formations) -->
            <div class="split-panel left-panel" id="trainings-list-panel">
              <div class="panel-header">
                <div class="domain-filter" id="domain-filter">
                  <!-- La liste des domaines sera générée dynamiquement par JavaScript -->
                  <div class="loading-message">Chargement des domaines...</div>
                </div>
              </div>
              <div class="panel-content">
                <div class="trainings-list" id="trainings-list">
                  <!-- La liste des formations sera générée dynamiquement par JavaScript -->
                  <div class="loading-message">Chargement des formations...</div>
                </div>
              </div>
            </div>

            <!-- Séparateur vertical redimensionnable -->
            <div class="split-resizer vertical-resizer" id="vertical-resizer"></div>

            <!-- Panneau de droite (vue unique) -->
            <div class="split-panel right-panel" id="details-panel">
              <div class="panel-header">
                <h2 id="training-header">Veuillez sélectionner une formation</h2>
              </div>
              <div class="panel-content">
                <div class="training-details" id="training-details">
                  <div class="placeholder-message">Veuillez sélectionner une formation</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions Tab Content -->
        <div class="tab-content" id="actions-content">
          <h1>Actions List</h1>
          <div class="actions-container">
            <div class="action-item">
              <div class="action-icon"><i class="fas fa-file-export"></i></div>
              <div class="action-details">
                <h3>Export Data</h3>
                <p>Export data to various formats</p>
              </div>
              <button class="action-btn">Execute</button>
            </div>
            <div class="action-item">
              <div class="action-icon"><i class="fas fa-sync"></i></div>
              <div class="action-details">
                <h3>Refresh Data</h3>
                <p>Update all data sources</p>
              </div>
              <button class="action-btn">Execute</button>
            </div>
            <div class="action-item">
              <div class="action-icon"><i class="fas fa-cog"></i></div>
              <div class="action-details">
                <h3>Settings</h3>
                <p>Configure application settings</p>
              </div>
              <button class="action-btn">Execute</button>
            </div>
            <div class="action-item">
              <div class="action-icon"><i class="fas fa-user-cog"></i></div>
              <div class="action-details">
                <h3>User Management</h3>
                <p>Manage user accounts and permissions</p>
              </div>
              <button class="action-btn">Execute</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Logo and Brand Text -->
    <div class="app-footer">
      <div class="brand-container">
        <img src="gsa.png" alt="GSA Logo" class="brand-logo" />
        <span class="brand-separator">|</span>
        <span class="brand-text">Groupe de Soutien Artillerie</span>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="search-container">
      <div class="search-bar">
        <input type="text" id="search-input" placeholder="Rechercher..." />
        <button id="search-button">
          <i class="fas fa-search"></i>
        </button>
      </div>
    </div>

    <!-- Date Time Display, Fullscreen and Theme Switcher -->
    <div class="bottom-controls">
      <div class="datetime-display" id="datetime-display">
        16 octobre 2025, 22h30
      </div>
      <div class="fullscreen-toggle" id="fullscreen-toggle">
        <i class="fas fa-expand"></i>
      </div>
      <div class="theme-switcher" id="theme-switcher">
        <i class="fas fa-sun"></i>
      </div>
    </div>

    <!-- Leaflet JS -->
    <script src="assets/leaflet/leaflet.js"></script>
    <!-- Chart.js -->
    <script src="assets/chart.js/chart.min.js"></script>
    <!-- Main Application Script -->
    <script src="renderer.js"></script>

    <!-- Dashboard Tabs Script -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Initialize dashboard tabs
        const dashboardTabs = document.querySelectorAll('.dashboard-tab');
        const tabContents = document.querySelectorAll('.dashboard-tab-content');

        // Add click event listeners to all dashboard tabs
        dashboardTabs.forEach(tab => {
          tab.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // Remove active class from all tabs
            dashboardTabs.forEach(t => t.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Hide all tab contents
            tabContents.forEach(content => content.classList.remove('active'));

            // Show target tab content
            const targetContent = document.getElementById(targetTab + '-content');
            if (targetContent) {
              targetContent.classList.add('active');
            }

            console.log('Switched to dashboard tab:', targetTab);

            // Handle specific tab initialization if needed
            handleTabSwitch(targetTab);
          });
        });

        function handleTabSwitch(tabName) {
          switch(tabName) {
            case 'activites':
              console.log('Activités tab selected - showing schedule and activities timeline');
              // The content is already in the activites-content div
              break;
            case 'kpis':
              console.log('KPIs tab selected - showing statistics');
              // The KPI content is already in the kpis-content div
              break;
            case 'missions':
              console.log('Missions tab selected');
              // Future implementation for missions
              break;
            case 'effectif':
              console.log('Effectif tab selected');
              // Future implementation for effectif
              break;
            case 'dto':
              console.log('DTO tab selected');
              // Future implementation for DTO
              break;
            default:
              console.log('Unknown tab:', tabName);
          }
        }
      });
    </script>

    <!-- Direct Map Initialization Script -->
    <script>
      // This script directly initializes the map when the page loads
      document.addEventListener('DOMContentLoaded', function () {
        console.log('Direct map initialization script loaded');

        // Initialize map after a delay
        setTimeout(function () {
          console.log('Attempting direct map initialization');

          try {
            // Get the map element
            const mapElement = document.getElementById('morocco-map');
            if (!mapElement) {
              console.error('Map element not found in direct script');
              return;
            }

            console.log('Map element found in direct script:', mapElement);
            console.log(
              'Map element dimensions:',
              mapElement.offsetWidth,
              'x',
              mapElement.offsetHeight
            );

            // Force the map element to be visible
            mapElement.style.display = 'block';
            mapElement.style.height = '100%';
            mapElement.style.width = '100%';
            mapElement.style.position = 'relative';

            // Check if Leaflet is loaded
            if (typeof L === 'undefined') {
              console.error('Leaflet not loaded in direct script');
              return;
            }

            console.log('Leaflet is loaded in direct script');

            // Check if map is already initialized
            if (mapElement._leaflet_id) {
              console.log('Map already initialized in direct script');
              return;
            }

            // Define initial map center and zoom level
            const initialCenter = [28.5, -8.0]; // Further adjusted center to position Morocco even higher in the view
            const initialZoom = 6.0; // Initial zoom level set to 6 as requested

            // Create the map
            console.log('Creating map in direct script...');
            var directMap = L.map('morocco-map', {
              center: initialCenter,
              zoom: initialZoom,
              minZoom: 5.0, // Minimum zoom level
              maxZoom: 10,
              zoomControl: true,
              attributionControl: false, // Hide attribution control
              zoomSnap: 0.10, // Allow finer zoom control
              zoomDelta: 0.5, // Smoother zoom steps
              wheelPxPerZoomLevel: 120 // More sensitive mouse wheel zooming
            });

            console.log('Map created in direct script');

            // Add map controls (reset button and zoom level display)
            addZoomLevelDisplay(directMap);

            // Load Morocco GeoJSON data
            console.log('Loading Morocco GeoJSON data...');

            // Define style for the GeoJSON - simple style for Morocco boundaries
            function style(feature) {
              return {
                fillColor: '#333333', // dodgerblue
                weight: 0.5,
                opacity: 1,
                color: 'white',
                fillOpacity: 0.6,
                smoothFactor: 1,
              };
            }

            // Simple feature handler - no interactions
            function onEachFeature(feature, layer) {
              // No interactions needed, just display the boundaries
            }

            // Create a variable to hold the GeoJSON layer
            var moroccoGeoJSON;

            // For testing error handling - set to true to simulate error
            var simulateError = false;

            // Fetch the GeoJSON file
            console.log('Fetching morocco.geojson file...');

            // Direct fetch of morocco.geojson as requested with cache-busting
            fetch('assets/data/morocco.geojson?v=' + new Date().getTime())
              .then(function (response) {
                console.log('GeoJSON fetch response status:', response.status);
                if (!response.ok) {
                  throw new Error(
                    'Network response was not ok: ' + response.status
                  );
                }
                console.log('Parsing GeoJSON response...');
                return response.json();
              })
              .then(function (data) {
                console.log(
                  'GeoJSON data loaded successfully, features:',
                  data.features ? data.features.length : 'none'
                );

                try {
                  // Add the GeoJSON layer to the map
                  console.log('Creating GeoJSON layer...');
                  moroccoGeoJSON = L.geoJSON(data, {
                    style: style,
                    onEachFeature: onEachFeature,
                  });

                  console.log('Adding GeoJSON layer to map...');
                  moroccoGeoJSON.addTo(directMap);

                  console.log('Fitting map to GeoJSON bounds...');
                  directMap.fitBounds(moroccoGeoJSON.getBounds());

                  console.log('Morocco GeoJSON added to map successfully');

                  // Legend removed as requested

                  // Buttons removed as requested

                  // Fetching and displaying drone deployments disabled as requested
                  console.log('Drone fetching and display disabled as requested');

                  // Set up map toggle buttons
                  setupMapToggleButtons();
                } catch (err) {
                  console.error('Error processing GeoJSON data:', err);
                  throw err;
                }
              })
              .catch(function (error) {
                console.error('Error loading GeoJSON:', error);

                // Display an error message in the map container
                console.log(
                  'Displaying error message for GeoJSON load failure'
                );

                // Create an error message element
                var errorDiv = document.createElement('div');
                errorDiv.className = 'map-error-message';
                errorDiv.innerHTML = `
                  <div class="error-icon"><i class="fas fa-exclamation-triangle"></i></div>
                  <h3>Erreur de chargement de la carte</h3>
                  <p>Impossible de charger les données cartographiques du Maroc.</p>
                  <p class="error-details">Détails: ${error.message}</p>
                  <p class="error-path">Chemin du fichier: assets/data/morocco.geojson</p>
                `;

                // Clear the map container and add the error message
                var mapContainer = document.getElementById('morocco-map');

                if (!mapContainer) {
                  console.error('Map container not found for error message');
                  return;
                }

                console.log('Map container found, adding error message');

                // Create a centered container for the error message
                var centerDiv = document.createElement('div');
                centerDiv.className = 'map-error-container';
                centerDiv.appendChild(errorDiv);

                // Add the error container to the map
                try {
                  mapContainer.innerHTML = '';
                  mapContainer.appendChild(centerDiv);

                  // Set background color
                  mapContainer.style.backgroundColor = 'var(--bg-secondary)';

                  console.log('Error message added to map container');
                } catch (err) {
                  console.error(
                    'Error adding error message to map container:',
                    err
                  );
                }
              });

            // Global variable to store the drone layer group
            let droneLayerGroup = null;

            // Function to fetch and display drone deployments
            async function fetchAndDisplayDroneDeployments(map) {
              console.log('Fetching drone deployments...');

              // Show loading indicator
              const mapContainer = document.querySelector('.map-container');
              const loadingIndicator = window.showLoading(mapContainer);

              try {
                // Fetch drone deployments from the database
                const response = await window.api.getDroneDeployments();

                // Add a small delay to ensure the loading animation is visible
                await new Promise(resolve => setTimeout(resolve, 800));

                // Hide loading indicator
                window.hideLoading(mapContainer);

                if (!response.success) {
                  console.error('Error fetching drone deployments:', response.error);
                  alert('Error fetching drone data: ' + response.error);
                  return;
                }

                const deployments = response.data;
                console.log('Drone deployments fetched:', JSON.stringify(deployments));
                console.log(`Total drone records: ${deployments.length}`);

                // Remove existing drone layer group if it exists
                if (droneLayerGroup) {
                  map.removeLayer(droneLayerGroup);
                }

                // Create a new layer group for all drone markers
                droneLayerGroup = L.layerGroup().addTo(map);

                // Clear the drone markers array
                droneMarkers = [];

                // Check if we have any deployments
                if (!deployments || deployments.length === 0) {
                  console.warn('No drone deployments found in database');
                  alert('No drone deployments found in database');
                  return;
                }

                // Process each drone deployment
                for (let i = 0; i < deployments.length; i++) {
                  const drone = deployments[i];

                  console.log(`Processing drone ${i+1}/${deployments.length}: ID=${drone.id}, Type=${drone.type_drone}, Position=[${drone.lat}, ${drone.lng}]`);

                  // Validate drone data
                  if (!drone.lat || !drone.lng || !drone.type_drone) {
                    console.warn(`Skipping drone ${i+1} due to missing data:`, drone);
                    continue;
                  }

                  // Convert lat/lng to numbers if they're strings
                  const lat = typeof drone.lat === 'string' ? parseFloat(drone.lat) : drone.lat;
                  const lng = typeof drone.lng === 'string' ? parseFloat(drone.lng) : drone.lng;

                  // Store the drone position for fitting bounds
                  droneMarkers.push([lat, lng]);

                  // Create marker for the drone position - simple grey circle
                  const droneMarker = L.circleMarker([lat, lng], {
                    radius: 5, // Small circle
                    fillColor: '#808080', // Grey for all markers
                    color: '#fff',
                    weight: 1, // Thin border
                    opacity: 1,
                    fillOpacity: 0.8
                  }).addTo(droneLayerGroup);

                  // Calculate optimal label position to avoid overlaps
                  const labelPosition = calculateLabelPosition(lat, lng, droneMarkers, i);

                  // Get the calculated label position
                  const labelLat = labelPosition.lat;
                  const labelLng = labelPosition.lng;

                  // Create a label with the simplified information
                  const labelIcon = L.divIcon({
                    className: 'drone-label',
                    html: `<div class="drone-label-content">
                      <div class="drone-city">${drone.ville_implantation || 'N/A'}</div>
                      <div class="drone-info">
                        ${drone.unite || 'N/A'}
                      </div>
                      <div class="drone-type-container">
                        <span class="drone-type">${drone.type_drone || 'N/A'}</span>
                      </div>
                    </div>`,
                    iconSize: [220, 65], // Size for three lines with styling
                    iconAnchor: [0, 30]  // Adjusted anchor point
                  });

                  // Add the label marker at the offset position
                  const labelMarker = L.marker([labelLat, labelLng], {
                    icon: labelIcon,
                    zIndexOffset: 1000 // Ensure labels are above other markers
                  }).addTo(droneLayerGroup);

                  // Create a subtle line connecting the marker to the label
                  const connectorLine = L.polyline(
                    [
                      [lat, lng],
                      [labelLat, labelLng] // Use calculated offset position
                    ],
                    {
                      color: 'var(--text-secondary)', // Use theme color
                      weight: 0.8, // Very thin line
                      opacity: 0.5, // Subtle
                      dashArray: '2,4'
                    }
                  ).addTo(droneLayerGroup);

                  // Add popup with simplified drone details
                  droneMarker.bindPopup(`
                    <div class="drone-popup">
                      <h3>Drone #${drone.id}</h3>
                      <p>Type: ${drone.type_drone || 'N/A'}</p>
                      <p>Unité: ${drone.unite || 'N/A'}</p>
                      <p>Ville: ${drone.ville_implantation || 'N/A'}</p>
                    </div>
                  `);

                  console.log(`Added drone marker ${i+1} to map`);
                }

                console.log(`Displayed ${droneMarkers.length} drone markers on map`);

                // Automatic refresh disabled as requested
                console.log('Automatic refresh disabled as requested');
              } catch (error) {
                console.error('Error displaying drone deployments:', error);
                alert('Error displaying drone deployments: ' + error.message);
              }
            }

            // Variable to store the refresh interval
            let droneRefreshInterval = null;

            // Function to set up automatic refresh of drone positions
            function setupDroneRefresh(map) {
              // Clear any existing interval
              if (droneRefreshInterval) {
                clearInterval(droneRefreshInterval);
              }

              // Set up new interval to refresh drone positions every 30 seconds
              droneRefreshInterval = setInterval(async () => {
                console.log('Refreshing drone positions...');
                try {
                  // Fetch drone deployments from the database
                  const response = await window.api.getDroneDeployments();

                  if (!response.success) {
                    console.error('Error refreshing drone deployments:', response.error);
                    return;
                  }

                  const deployments = response.data;

                  // Remove existing drone layer group
                  if (droneLayerGroup) {
                    map.removeLayer(droneLayerGroup);
                  }

                  // Create a new layer group for all drone markers
                  droneLayerGroup = L.layerGroup().addTo(map);

                  // Clear the drone markers array
                  droneMarkers = [];

                  // Check if we have any deployments
                  if (!deployments || deployments.length === 0) {
                    console.warn('No drone deployments found in database during refresh');
                    return;
                  }

                  // Process each drone deployment
                  for (let i = 0; i < deployments.length; i++) {
                    const drone = deployments[i];

                    // Validate drone data
                    if (!drone.lat || !drone.lng || !drone.type_drone) {
                      console.warn(`Skipping drone ${i+1} during refresh due to missing data:`, drone);
                      continue;
                    }

                    // Convert lat/lng to numbers if they're strings
                    const lat = typeof drone.lat === 'string' ? parseFloat(drone.lat) : drone.lat;
                    const lng = typeof drone.lng === 'string' ? parseFloat(drone.lng) : drone.lng;

                    // Store the drone position for fitting bounds
                    droneMarkers.push([lat, lng]);

                    // Create marker for the drone position - simple grey circle
                    const droneMarker = L.circleMarker([lat, lng], {
                      radius: 5, // Small circle
                      fillColor: '#808080', // Grey for all markers
                      color: '#fff',
                      weight: 1, // Thin border
                      opacity: 1,
                      fillOpacity: 0.8
                    }).addTo(droneLayerGroup);

                    // Calculate optimal label position to avoid overlaps
                    const labelPosition = calculateLabelPosition(lat, lng, droneMarkers, i);

                    // Get the calculated label position
                    const labelLat = labelPosition.lat;
                    const labelLng = labelPosition.lng;

                    // Create a label with the simplified information
                    const labelIcon = L.divIcon({
                      className: 'drone-label',
                      html: `<div class="drone-label-content">
                        <div class="drone-city">${drone.ville_implantation || 'N/A'}</div>
                        <div class="drone-info">
                          ${drone.unite || 'N/A'}
                        </div>
                        <div class="drone-type-container">
                          <span class="drone-type">${drone.type_drone || 'N/A'}</span>
                        </div>
                      </div>`,
                      iconSize: [220, 65], // Size for three lines with styling
                      iconAnchor: [0, 30]  // Adjusted anchor point
                    });

                    // Add the label marker at the offset position
                    const labelMarker = L.marker([labelLat, labelLng], {
                      icon: labelIcon,
                      zIndexOffset: 1000 // Ensure labels are above other markers
                    }).addTo(droneLayerGroup);

                    // Create a subtle line connecting the marker to the label
                    const connectorLine = L.polyline(
                      [
                        [lat, lng],
                        [labelLat, labelLng] // Use calculated offset position
                      ],
                      {
                        color: 'var(--text-secondary)', // Use theme color
                        weight: 0.8, // Very thin line
                        opacity: 0.5, // Subtle
                        dashArray: '2,4'
                      }
                    ).addTo(droneLayerGroup);

                    // Add popup with simplified drone details
                    droneMarker.bindPopup(`
                      <div class="drone-popup">
                        <h3>Drone #${drone.id}</h3>
                        <p>Type: ${drone.type_drone || 'N/A'}</p>
                        <p>Unité: ${drone.unite || 'N/A'}</p>
                        <p>Ville: ${drone.ville_implantation || 'N/A'}</p>
                      </div>
                    `);
                  }

                  console.log(`Refreshed ${droneMarkers.length} drone markers on map`);

                  console.log('Drone positions refreshed');
                } catch (error) {
                  console.error('Error refreshing drone positions:', error);
                }
              }, 30000); // Refresh every 30 seconds
            }

            // Helper function to get color based on drone type
            function getDroneColor(droneType) {
              const colorMap = {
                'Surveillance': '#1E90FF', // DodgerBlue
                'Reconnaissance': '#32CD32', // LimeGreen
                'Attack': '#FF4500', // OrangeRed
                'Transport': '#FFD700', // Gold
                'Medical': '#FF69B4', // HotPink
              };

              return colorMap[droneType] || '#808080'; // Default to gray if type not found
            }

            // Helper function to calculate optimal label position to avoid overlaps
            function calculateLabelPosition(lat, lng, allMarkers, index, defaultOffset = 0.15) {
              // Define possible positions around the circle (8 directions)
              const positions = [
                { lat: defaultOffset, lng: 0 },            // North
                { lat: defaultOffset, lng: defaultOffset }, // Northeast
                { lat: 0, lng: defaultOffset },            // East
                { lat: -defaultOffset, lng: defaultOffset }, // Southeast
                { lat: -defaultOffset, lng: 0 },           // South
                { lat: -defaultOffset, lng: -defaultOffset }, // Southwest
                { lat: 0, lng: -defaultOffset },           // West
                { lat: defaultOffset, lng: -defaultOffset }  // Northwest
              ];

              // If this is the first marker or there are no other markers, use default Northeast position
              if (allMarkers.length <= 1 || index === 0) {
                return {
                  lat: lat + positions[1].lat,
                  lng: lng + positions[1].lng
                };
              }

              // Calculate distances to all other markers
              const distances = [];
              for (let i = 0; i < allMarkers.length; i++) {
                if (i !== index) {
                  const otherLat = allMarkers[i][0];
                  const otherLng = allMarkers[i][1];
                  const distance = Math.sqrt(
                    Math.pow(lat - otherLat, 2) +
                    Math.pow(lng - otherLng, 2)
                  );
                  distances.push({ index: i, distance });
                }
              }

              // Sort by distance (closest first)
              distances.sort((a, b) => a.distance - b.distance);

              // If closest marker is far enough away, use default position
              if (distances.length === 0 || distances[0].distance > defaultOffset * 3) {
                return {
                  lat: lat + positions[1].lat,
                  lng: lng + positions[1].lng
                };
              }

              // Get the closest marker
              const closestMarker = allMarkers[distances[0].index];
              const closestLat = closestMarker[0];
              const closestLng = closestMarker[1];

              // Determine which direction to place the label based on relative position
              // of the closest marker
              let bestPosition;

              if (closestLat > lat && closestLng > lng) {
                // Closest marker is Northeast, so place label Southwest
                bestPosition = positions[5];
              } else if (closestLat > lat && closestLng < lng) {
                // Closest marker is Northwest, so place label Southeast
                bestPosition = positions[3];
              } else if (closestLat < lat && closestLng > lng) {
                // Closest marker is Southeast, so place label Northwest
                bestPosition = positions[7];
              } else if (closestLat < lat && closestLng < lng) {
                // Closest marker is Southwest, so place label Northeast
                bestPosition = positions[1];
              } else if (closestLat > lat) {
                // Closest marker is North, so place label South
                bestPosition = positions[4];
              } else if (closestLat < lat) {
                // Closest marker is South, so place label North
                bestPosition = positions[0];
              } else if (closestLng > lng) {
                // Closest marker is East, so place label West
                bestPosition = positions[6];
              } else {
                // Closest marker is West, so place label East
                bestPosition = positions[2];
              }

              return {
                lat: lat + bestPosition.lat,
                lng: lng + bestPosition.lng
              };
            }

            // Function to fetch and display missile module deployments
            async function fetchAndDisplayMissileDeployments(map) {
              console.log('Fetching missile deployments...');

              // Show loading indicator
              const mapContainer = document.querySelector('.map-container');
              const loadingIndicator = window.showLoading(mapContainer);

              try {
                // Fetch missile deployments from the database
                const response = await window.api.getMissileDeployments();

                // Add a small delay to ensure the loading animation is visible
                await new Promise(resolve => setTimeout(resolve, 800));

                // Hide loading indicator
                window.hideLoading(mapContainer);

                if (!response.success) {
                  console.error('Error fetching missile deployments:', response.error);
                  alert('Error fetching missile data: ' + response.error);
                  return;
                }

                const deployments = response.data;
                console.log('Missile deployments fetched:', JSON.stringify(deployments));
                console.log(`Total missile records: ${deployments.length}`);

                // Remove existing missile layer group if it exists
                if (missileLayerGroup) {
                  map.removeLayer(missileLayerGroup);
                }

                // Create a new layer group for all missile markers
                missileLayerGroup = L.layerGroup().addTo(map);

                // Clear the missile markers array
                missileMarkers = [];

                // Check if we have any deployments
                if (!deployments || deployments.length === 0) {
                  console.warn('No missile deployments found in database');
                  alert('No missile deployments found in database');
                  return;
                }

                // Process each missile deployment
                for (let i = 0; i < deployments.length; i++) {
                  const missile = deployments[i];

                  console.log(`Processing missile ${i+1}/${deployments.length}: ID=${missile.id}, Module=${missile.module_bie}, Position=[${missile.lat}, ${missile.lng}]`);

                  // Validate missile data
                  if (!missile.lat || !missile.lng || !missile.module_bie) {
                    console.warn(`Skipping missile ${i+1} due to missing data:`, missile);
                    continue;
                  }

                  // Convert lat/lng to numbers if they're strings
                  const lat = typeof missile.lat === 'string' ? parseFloat(missile.lat) : missile.lat;
                  const lng = typeof missile.lng === 'string' ? parseFloat(missile.lng) : missile.lng;

                  // Store the missile position for fitting bounds
                  missileMarkers.push([lat, lng]);

                  // Create marker for the missile position - simple grey circle
                  const missileMarker = L.circleMarker([lat, lng], {
                    radius: 5, // Small circle
                    fillColor: '#808080', // Grey for all markers
                    color: '#fff',
                    weight: 1, // Thin border
                    opacity: 1,
                    fillOpacity: 0.8
                  }).addTo(missileLayerGroup);

                  // Calculate optimal label position to avoid overlaps
                  const labelPosition = calculateLabelPosition(lat, lng, missileMarkers, i);

                  // Get the calculated label position
                  const labelLat = labelPosition.lat;
                  const labelLng = labelPosition.lng;

                  // Create a label with the formatted information
                  const labelIcon = L.divIcon({
                    className: 'missile-label',
                    html: `<div class="missile-label-content">
                      <div class="missile-city">${missile.ville_implantation || 'N/A'}</div>
                      <div class="missile-info">
                        ${missile.module_bie || 'N/A'} | ${missile.unite || 'N/A'}
                      </div>
                      <div class="missile-type-container">
                        <span class="missile-type">${missile.type_missile || 'N/A'}</span>
                      </div>
                    </div>`,
                    iconSize: [220, 65], // Increased size for three lines with styling
                    iconAnchor: [0, 30]  // Adjusted anchor point
                  });

                  // Add the label marker at the offset position
                  const labelMarker = L.marker([labelLat, labelLng], {
                    icon: labelIcon,
                    zIndexOffset: 1000 // Ensure labels are above other markers
                  }).addTo(missileLayerGroup);

                  // Create a subtle line connecting the marker to the label
                  const connectorLine = L.polyline(
                    [
                      [lat, lng],
                      [labelLat, labelLng] // Use calculated offset position
                    ],
                    {
                      color: 'var(--text-secondary)', // Use theme color
                      weight: 0.8, // Very thin line
                      opacity: 0.5, // Subtle
                      dashArray: '2,4'
                    }
                  ).addTo(missileLayerGroup);

                  // Add popup with missile details
                  missileMarker.bindPopup(`
                    <div class="missile-popup">
                      <h3>Module #${missile.id}</h3>
                      <p>Unité: ${missile.unite || 'N/A'}</p>
                      <p>Module: ${missile.module_bie}</p>
                      <p>Matériel: ${missile.materiel || 'N/A'}</p>
                      <p>Type Missile: ${missile.type_missile || 'N/A'}</p>
                      <p>Ville: ${missile.ville_implantation || 'N/A'}</p>
                      <p>Position: ${lat.toFixed(6)}, ${lng.toFixed(6)}</p>
                    </div>
                  `);

                  console.log(`Added missile marker ${i+1} to map`);
                }

                console.log(`Displayed ${missileMarkers.length} missile markers on map`);

              } catch (error) {
                console.error('Error displaying missile deployments:', error);
                alert('Error displaying missile deployments: ' + error.message);
              }
            }

            // Legend function removed as requested

            // Global variable to store drone markers for fitting bounds
            let droneMarkers = [];

            // Global variables for missile markers
            let missileLayerGroup = null;
            let missileMarkers = [];

            // Function to fit the map to show all drone markers (kept for potential future use)
            function fitMapToDrones(map) {
              if (droneMarkers.length === 0) {
                console.log('No drone markers to fit');
                return;
              }

              // Create a bounds object
              const bounds = L.latLngBounds(droneMarkers);

              // Fit the map to the bounds with some padding
              map.fitBounds(bounds, {
                padding: [50, 50],
                maxZoom: 8
              });

              console.log('Map fitted to show all drones');
            }

            // Function to clear all data layers from the map
            function clearAllMapLayers(map) {
              console.log('Clearing all map layers...');

              // Clear drone layer if it exists
              if (droneLayerGroup) {
                map.removeLayer(droneLayerGroup);
                droneLayerGroup = null;
                droneMarkers = [];
                console.log('Drone layer removed');
              }

              // Clear missile layer if it exists
              if (missileLayerGroup) {
                map.removeLayer(missileLayerGroup);
                missileLayerGroup = null;
                missileMarkers = [];
                console.log('Missile layer removed');
              }

              // Add more layer clearing here as needed for future layer types

              // Show the GSA logo overlay when no layers are active
              const mapLogoOverlay = document.getElementById('map-logo-overlay');
              if (mapLogoOverlay) {
                mapLogoOverlay.style.display = 'block';
              }

              // Ensure map is visible and properly sized
              const mapElement = document.getElementById('morocco-map');
              if (mapElement && directMap) {
                // Force the map element to be visible
                mapElement.style.display = 'block';
                mapElement.style.height = '100%';
                mapElement.style.width = '100%';
                mapElement.style.position = 'relative';

                // Invalidate size to fix any rendering issues
                directMap.invalidateSize();

                // Reset view to ensure Morocco is centered
                directMap.setView(initialCenter, initialZoom);
              }

              console.log('All map layers cleared');
            }

            // Function to add a zoom level display to the map
            function addZoomLevelDisplay(map) {
              // Create a custom control that will appear next to the zoom controls
              const zoomDisplay = L.control({ position: 'topleft' });

              // When the control is added to the map
              zoomDisplay.onAdd = function(map) {
                // Create a container for both the reset button and zoom display
                const container = L.DomUtil.create('div', 'map-controls-container');

                // Create the reset button
                const resetButton = L.DomUtil.create('button', 'map-reset-button', container);
                resetButton.innerHTML = '<i class="fas fa-crosshairs"></i>'; // Changed to a crosshairs/target icon for recenter
                resetButton.title = 'Recenter map';

                // Add click event listener to reset button
                L.DomEvent.on(resetButton, 'click', function(e) {
                  L.DomEvent.stopPropagation(e);
                  L.DomEvent.preventDefault(e);

                  // Reset the map view to the initial center and zoom
                  map.setView(initialCenter, initialZoom, {
                    animate: true,
                    duration: 1.0,
                    noMoveStart: true
                  });

                  // Ensure the zoom is exactly the initial zoom after a short delay
                  setTimeout(() => {
                    if (map.getZoom() !== initialZoom) {
                      map.setZoom(initialZoom);
                      map.fire('zoomend');
                    }
                    console.log('Map view reset to initial position with zoom level:', map.getZoom());
                  }, 100);
                });

                // Create the zoom level display
                const zoomLevelDisplay = L.DomUtil.create('div', 'zoom-level-display', container);
                zoomLevelDisplay.innerHTML = `Zoom | ${map.getZoom().toFixed(1)}`;

                // Update the zoom level display whenever the zoom changes
                map.on('zoomend', function() {
                  zoomLevelDisplay.innerHTML = `Zoom | ${map.getZoom().toFixed(1)}`;
                });

                // Also update on moveend to catch any zoom changes from other sources
                map.on('moveend', function() {
                  zoomLevelDisplay.innerHTML = `Zoom | ${map.getZoom().toFixed(1)}`;
                });

                // Prevent map click events when clicking the controls
                L.DomEvent.disableClickPropagation(container);

                return container;
              };

              // Add the control to the map
              zoomDisplay.addTo(map);
            }

            // Reset button functionality has been integrated into the zoom level display function

            // Function to set up map toggle buttons
            function setupMapToggleButtons() {
              const toggleButtons = document.querySelectorAll('.map-toggle-btn');
              const mapLogoOverlay = document.getElementById('map-logo-overlay');

              // Function to ensure map is visible and properly sized
              function ensureMapIsVisible() {
                // Get the map element
                const mapElement = document.getElementById('morocco-map');
                if (!mapElement) return;

                // Force the map element to be visible
                mapElement.style.display = 'block';
                mapElement.style.height = '100%';
                mapElement.style.width = '100%';
                mapElement.style.position = 'relative';

                // Invalidate size to fix any rendering issues
                if (directMap) {
                  console.log('Refreshing map size');
                  directMap.invalidateSize();

                  // Reset view to ensure Morocco is centered
                  directMap.setView(initialCenter, initialZoom);
                }
              }

              // Function to check if any toggle button is active
              function isAnyToggleActive() {
                return Array.from(toggleButtons).some(btn => btn.classList.contains('active'));
              }

              // Function to update logo visibility
              function updateLogoVisibility() {
                if (isAnyToggleActive()) {
                  // Hide logo when any toggle is active
                  mapLogoOverlay.style.display = 'none';
                } else {
                  // Show logo when no toggle is active
                  mapLogoOverlay.style.display = 'block';
                }
              }

              // Initial setup - ensure map is visible and show logo if no toggle is active
              ensureMapIsVisible();
              updateLogoVisibility();

              // Add click event listeners to each button
              toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                  const layer = this.getAttribute('data-layer');
                  const isCurrentlyActive = this.classList.contains('active');

                  // First, remove active class from all buttons
                  toggleButtons.forEach(btn => {
                    btn.classList.remove('active');
                  });

                  // First, clear all layers from the map regardless of which button was clicked
                  clearAllMapLayers(directMap);

                  // Ensure map is visible
                  ensureMapIsVisible();

                  // If the clicked button wasn't already active, make it active and display its data
                  if (!isCurrentlyActive) {
                    this.classList.add('active');
                    console.log(`Toggle ${layer} layer: ON`);

                    // Handle different layer types
                    switch(layer) {
                      case 'missiles':
                        // Fetch and display missile deployments
                        fetchAndDisplayMissileDeployments(directMap);
                        break;
                      case 'drones':
                        // Fetch and display drone deployments
                        fetchAndDisplayDroneDeployments(directMap);
                        break;
                      case 'personnel':
                        console.log('Personnel drone layer not implemented yet');
                        break;
                      case 'stats':
                        console.log('Statistics layer not implemented yet');
                        break;
                      default:
                        console.log(`Unknown layer type: ${layer}`);
                    }
                  } else {
                    // If the button was already active, just turn it off
                    // The map is already cleared at the beginning of this function
                    console.log(`Toggle ${layer} layer: OFF`);
                  }

                  // Update logo visibility based on toggle state
                  updateLogoVisibility();
                });
              });

              console.log('Map toggle buttons initialized');
            }

            // No need for a popup here as it's handled in the GeoJSON implementation

            // Map bounds are set in the GeoJSON implementation

            // Add click handler to map tab to refresh the map
            document.querySelectorAll('.tab').forEach(function (tab) {
              const tabName = tab.getAttribute('data-tab');

              if (tabName === 'map') {
                tab.addEventListener('click', function () {
                  console.log('Map tab clicked in direct script');
                  setTimeout(function () {
                    if (directMap) {
                      console.log('Refreshing map size in direct script');

                      // Ensure map is visible and properly sized
                      const mapElement = document.getElementById('morocco-map');
                      if (mapElement) {
                        // Force the map element to be visible
                        mapElement.style.display = 'block';
                        mapElement.style.height = '100%';
                        mapElement.style.width = '100%';
                        mapElement.style.position = 'relative';
                      }

                      // Invalidate size to fix any rendering issues
                      directMap.invalidateSize();

                      // Reset view to ensure Morocco is centered
                      directMap.setView(initialCenter, initialZoom);

                      // Update logo visibility based on toggle button state
                      const mapLogoOverlay = document.getElementById('map-logo-overlay');
                      const isAnyToggleActive = Array.from(document.querySelectorAll('.map-toggle-btn')).some(btn => btn.classList.contains('active'));

                      if (mapLogoOverlay) {
                        mapLogoOverlay.style.display = isAnyToggleActive ? 'none' : 'block';
                      }

                      // Refresh drone positions disabled as requested
                      console.log('Drone fetching and display disabled as requested');
                    }
                  }, 300);
                });
              } else {
                // For other tabs, clear the drone refresh interval when switching away from map
                tab.addEventListener('click', function() {
                  if (droneRefreshInterval) {
                    console.log('Clearing drone refresh interval');
                    clearInterval(droneRefreshInterval);
                    droneRefreshInterval = null;
                  }
                });
              }
            });
          } catch (error) {
            console.error('Error in direct map initialization:', error);
          }
        }, 2000);
      });
    </script>

    <!-- Activity Details Modal -->
    <div id="activity-details-modal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2 id="modal-activity-title">Détails de l'activité</h2>
          <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
          <!-- Activity navigation for multiple activities on same date -->
          <div id="modal-activity-nav" class="activity-nav">
            <!-- Will be populated dynamically -->
          </div>

          <div class="activity-details">
            <div class="detail-row">
              <div class="detail-label">Activité:</div>
              <div id="modal-activity-name" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Date de début:</div>
              <div id="modal-activity-start-date" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Date de fin:</div>
              <div id="modal-activity-end-date" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Durée:</div>
              <div id="modal-activity-duration" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Lieu:</div>
              <div id="modal-activity-location" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Référence:</div>
              <div id="modal-activity-reference" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Observations:</div>
              <div id="modal-activity-observations" class="detail-value"></div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Source:</div>
              <div id="modal-activity-source" class="detail-value"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal pour tous les stagiaires de la formation -->
    <div id="all-trainees-modal" class="modal">
      <div class="modal-content all-trainees-modal-content">
        <div class="modal-header">
          <h2 id="all-trainees-modal-title">Tous les stagiaires - Formation</h2>
          <span class="close" id="close-all-trainees-modal">&times;</span>
        </div>
        <div class="modal-body">
          <!-- Barre de recherche et filtres -->
          <div class="modal-search-container">
            <div class="search-bar-modal">
              <input type="text" id="all-trainees-search" placeholder="Rechercher par nom, prénom, grade, unité, fonction..." />
              <button type="button" id="clear-search-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <!-- Filtre par instance -->
            <div class="instance-filter-container">
              <select id="all-trainees-instance-filter" class="instance-filter">
                <option value="">Toutes les instances</option>
              </select>
            </div>
            <!-- Filtre par fonction -->
            <div class="fonction-filter-container">
              <select id="all-trainees-fonction-filter" class="fonction-filter">
                <option value="">Toutes les fonctions</option>
              </select>
            </div>
          </div>
          <!-- Conteneur de la table -->
          <div class="all-trainees-table-container" id="all-trainees-table-container">
            <div class="loading-message">Chargement des stagiaires...</div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
