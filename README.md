# SMCA GSA OPS Dashboard

An Electron-based dashboard application for the Groupe de Soutien Artillerie (GSA) operations.

## Application Overview

This application provides a comprehensive dashboard for monitoring and managing GSA operations with the following features:

- **Completely offline functionality** - All resources including CSS, fonts, and map data are stored locally
- **Database connectivity** - Connects to a MySQL database with automatic connection monitoring
- **Interactive UI** - Floating lateral tabs with smooth transitions and mouse wheel navigation
- **Responsive design** - Adapts to different screen sizes with fullscreen support
- **Theme switching** - Toggle between dark and light themes

## Technical Stack

- **Electron** - Cross-platform desktop application framework
- **HTML/CSS/JavaScript** - Frontend technologies
- **MySQL** - Database backend
- **Leaflet** - Interactive map library
- **Font Awesome** - Icon library (stored locally)

## Application Structure

### Main Files

- `index.html` - Main application HTML structure
- `styles.css` - Application styling
- `main.js` - Electron main process
- `renderer.js` - Renderer process for UI interactions
- `preload.js` - Secure bridge between renderer and main processes
- `database.js` - Database connection and query handling
- `error-logger.js` - Error logging functionality
- `setup_drones_table.sql` - SQL script to set up the drones deployment table

### Assets

- `/assets/data/` - Contains GeoJSON map data
- `/assets/leaflet/` - Leaflet library files
- `/assets/fontawesome/` - Font Awesome icon files

## Features

### Navigation System

The application uses a floating lateral tab system on the left side with four main sections:

1. **Dashboard** - Overview of key metrics and performance indicators
2. **Map** - Interactive map of Morocco using Leaflet and GeoJSON
3. **List** - Tabular data view with filtering and sorting capabilities
4. **Actions** - List of available actions and operations

### Database Integration

- Connects to a MySQL database named 'gsa'
- Default configuration uses localhost with username 'root' and password '065950'
- Continuously monitors database connection status
- Displays a French error message when disconnected
- Supports deployment where the database server may be on a different machine

### Map Functionality

- Interactive offline map of Morocco using Leaflet
- Uses GeoJSON data for rendering the map
- Configurable zoom levels (minimum zoom: 5.5)
- Error handling for map data loading failures
- Displays drone deployments from the database with:
  - Color-coded markers based on drone type
  - Labels showing drone type connected to markers with lines
  - Popups with detailed information
  - Automatic refresh every 30 seconds when the map tab is active

### UI Features

- **Theme Switcher** - Toggle between dark (VSCode-inspired) and light themes
- **Fullscreen Toggle** - Enter/exit fullscreen mode
- **Date/Time Display** - Shows current date and time in French format
- **Collapsible Sidebar** - Can be toggled to provide more screen space
- **Smooth Transitions** - Animated tab switching with slide effects
- **Mouse Wheel Navigation** - Switch between tabs by scrolling

## Configuration

### Database Settings

Database connection settings are defined in `database.js`:

```javascript
const dbConfig = {
  host: 'localhost', // Can be changed to server IP when deployed
  user: 'root',
  password: '065950',
  database: 'gsa',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
};
```

### Drone Deployment Table

The application displays drone deployments on the map. To set up the drone deployment table, run the `setup_drones_table.sql` script on your MySQL database. This script creates the `drones_deployment` table with the following structure:

```sql
CREATE TABLE IF NOT EXISTS `drones_deployment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lat` decimal(10,6) NOT NULL,
  `lng` decimal(10,6) NOT NULL,
  `type_drone` varchar(50) NOT NULL,
  `deployment_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

The script also inserts sample drone deployments across Morocco for testing purposes.

## Development Notes

- The application uses context isolation and a preload script for secure IPC communication
- Database connection is monitored every 10 seconds by default
- Map initialization includes error handling for GeoJSON loading failures
- Theme preference is stored in localStorage
- Tab state (collapsed/expanded) is persisted between sessions
- Drone positions are automatically refreshed when the map tab is active
