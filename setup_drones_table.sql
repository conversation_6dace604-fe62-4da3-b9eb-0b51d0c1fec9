-- SQL script to set up the drones_deployment table
-- Run this script on your MySQL database to create the table and insert sample data

-- Create the drones_deployment table if it doesn't exist
CREATE TABLE IF NOT EXISTS `drones_deployment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lat` decimal(10,6) NOT NULL,
  `lng` decimal(10,6) NOT NULL,
  `type_drone` varchar(50) NOT NULL,
  `deployment_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Clear existing data (optional)
TRUNCATE TABLE `drones_deployment`;

-- Insert sample drone deployments across Morocco
-- These are sample coordinates within Morocco's territory

-- Surveillance drones
INSERT INTO `drones_deployment` (`lat`, `lng`, `type_drone`) VALUES
(31.629472, -7.992047, 'Surveillance'),  -- Near Marrakech
(33.573109, -7.589843, 'Surveillance'),  -- Near Casablanca
(34.020882, -6.841650, 'Surveillance'),  -- Near Rabat
(35.169359, -5.268970, 'Surveillance'),  -- Near Tetouan
(30.427755, -9.598107, 'Surveillance');  -- Near Agadir

-- Reconnaissance drones
INSERT INTO `drones_deployment` (`lat`, `lng`, `type_drone`) VALUES
(32.336957, -6.360626, 'Reconnaissance'),  -- Central Morocco
(34.684947, -1.900873, 'Reconnaissance'),  -- Near Oujda
(31.047901, -4.017334, 'Reconnaissance'),  -- Eastern Morocco
(28.448454, -11.118584, 'Reconnaissance'), -- Western Sahara
(29.698242, -7.981808, 'Reconnaissance');  -- Southern Morocco

-- Attack drones
INSERT INTO `drones_deployment` (`lat`, `lng`, `type_drone`) VALUES
(33.879136, -5.554810, 'Attack'),  -- Near Fez
(31.508345, -5.129395, 'Attack'),  -- Near Ouarzazate
(35.005566, -2.631836, 'Attack'),  -- Northern Morocco
(27.940283, -12.885742, 'Attack'), -- Far Southern Morocco
(32.687031, -4.438477, 'Attack');  -- Eastern Morocco

-- Transport drones
INSERT INTO `drones_deployment` (`lat`, `lng`, `type_drone`) VALUES
(34.043310, -5.000000, 'Transport'),  -- Central Morocco
(32.323925, -9.238281, 'Transport'),  -- Western Morocco
(30.145127, -6.547852, 'Transport'),  -- Southern Morocco
(35.460670, -5.000000, 'Transport'),  -- Northern Morocco
(33.211116, -8.222656, 'Transport');  -- Western Morocco

-- Medical drones
INSERT INTO `drones_deployment` (`lat`, `lng`, `type_drone`) VALUES
(34.597042, -5.888672, 'Medical'),  -- Northern Morocco
(33.063924, -6.811523, 'Medical'),  -- Central Morocco
(31.802893, -7.294922, 'Medical'),  -- Near Marrakech
(35.317366, -3.999023, 'Medical'),  -- Northern Morocco
(29.840644, -9.404297, 'Medical');  -- Southern Morocco

-- Confirm the data was inserted
SELECT * FROM `drones_deployment`;
