// Error logger for debugging
const fs = require('fs');
const path = require('path');

// Override console.error to log to file
const originalConsoleError = console.error;
console.error = function() {
  // Call the original console.error
  originalConsoleError.apply(console, arguments);
  
  // Log to file
  const args = Array.from(arguments);
  const errorMessage = args.map(arg => 
    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
  ).join(' ');
  
  const logPath = path.join(__dirname, 'error.log');
  fs.appendFileSync(logPath, `${new Date().toISOString()}: ${errorMessage}\n`);
};

// Override console.log to log to file
const originalConsoleLog = console.log;
console.log = function() {
  // Call the original console.log
  originalConsoleLog.apply(console, arguments);
  
  // Log to file
  const args = Array.from(arguments);
  const logMessage = args.map(arg => 
    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
  ).join(' ');
  
  const logPath = path.join(__dirname, 'app.log');
  fs.appendFileSync(logPath, `${new Date().toISOString()}: ${logMessage}\n`);
};

module.exports = {
  // This is just a placeholder to make sure the module is loaded
  init: () => console.log('Error logger initialized')
};
